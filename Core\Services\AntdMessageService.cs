using System;
using System.Windows.Forms;
using TCP通讯.Core.Interfaces;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Core.Services
{
    /// <summary>
    /// AntdUI消息服务实现
    /// </summary>
    public class AntdMessageService : IMessageService
    {
        private readonly ILogger _logger;
        private Form _defaultForm;

        public AntdMessageService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 配置消息在窗体内显示
            AntdUI.Config.ShowInWindowByMessage = true;
        }

        /// <summary>
        /// 设置默认窗体
        /// </summary>
        /// <param name="form">默认窗体</param>
        public void SetDefaultForm(Form form)
        {
            _defaultForm = form;
        }

        public void ShowSuccess(string message, Form form = null)
        {
            try
            {
                var targetForm = form ?? _defaultForm;
                if (targetForm == null)
                {
                    _logger.LogWarning("未指定窗体，使用系统MessageBox显示成功消息");
                    MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 使用 AntdUI.Message.success 方法
                try
                {
                    AntdUI.Message.success(targetForm, message, null, 3);
                }
                catch
                {
                    // 如果 AntdUI 方法不可用，回退到 MessageBox
                    MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                _logger.LogDebug($"显示成功消息: {message}");
            }
            catch (Exception ex)
            {
                _logger.LogError("显示成功消息失败", ex);
                MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        public void ShowError(string message, Form form = null)
        {
            try
            {
                var targetForm = form ?? _defaultForm;
                if (targetForm == null)
                {
                    _logger.LogWarning("未指定窗体，使用系统MessageBox显示错误消息");
                    MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 使用 AntdUI.Message.error 方法
                try
                {
                    AntdUI.Message.error(targetForm, message, null, 3);
                }
                catch
                {
                    // 如果 AntdUI 方法不可用，回退到 MessageBox
                    MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                _logger.LogDebug($"显示错误消息: {message}");
            }
            catch (Exception ex)
            {
                _logger.LogError("显示错误消息失败", ex);
                MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void ShowWarning(string message, Form form = null)
        {
            try
            {
                var targetForm = form ?? _defaultForm;
                if (targetForm == null)
                {
                    _logger.LogWarning("未指定窗体，使用系统MessageBox显示警告消息");
                    MessageBox.Show(message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 使用 AntdUI.Message.warn 方法
                try
                {
                    AntdUI.Message.warn(targetForm, message, null, 3);
                }
                catch
                {
                    // 如果 AntdUI 方法不可用，回退到 MessageBox
                    MessageBox.Show(message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                _logger.LogDebug($"显示警告消息: {message}");
            }
            catch (Exception ex)
            {
                _logger.LogError("显示警告消息失败", ex);
                MessageBox.Show(message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        public void ShowInfo(string message, Form form = null)
        {
            try
            {
                var targetForm = form ?? _defaultForm;
                if (targetForm == null)
                {
                    _logger.LogWarning("未指定窗体，使用系统MessageBox显示信息消息");
                    MessageBox.Show(message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 使用 AntdUI.Message.info 方法
                try
                {
                    AntdUI.Message.info(targetForm, message, null, 3);
                }
                catch
                {
                    // 如果 AntdUI 方法不可用，回退到 MessageBox
                    MessageBox.Show(message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                _logger.LogDebug($"显示信息消息: {message}");
            }
            catch (Exception ex)
            {
                _logger.LogError("显示信息消息失败", ex);
                MessageBox.Show(message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        public void ShowNotification(string title, string message, NotificationType type = NotificationType.Info, Form form = null, int autoClose = 6)
        {
            try
            {
                var targetForm = form ?? _defaultForm;
                if (targetForm == null)
                {
                    _logger.LogWarning("未指定窗体，使用系统MessageBox显示通知");
                    MessageBox.Show($"{title}\n\n{message}", "通知", MessageBoxButtons.OK, GetMessageBoxIcon(type));
                    return;
                }

                // 暂时使用 MessageBox 替代，直到找到正确的 AntdUI 用法
                MessageBox.Show($"{title}\n\n{message}", "通知", MessageBoxButtons.OK, GetMessageBoxIcon(type));

                _logger.LogDebug($"显示通知: {title} - {message}");
            }
            catch (Exception ex)
            {
                _logger.LogError("显示通知失败", ex);
                MessageBox.Show($"{title}\n\n{message}", "通知", MessageBoxButtons.OK, GetMessageBoxIcon(type));
            }
        }

        public void ShowConfirm(string title, string message, Action onConfirm = null, Action onCancel = null, Form form = null)
        {
            try
            {
                var targetForm = form ?? _defaultForm;
                if (targetForm == null)
                {
                    _logger.LogWarning("未指定窗体，使用系统MessageBox显示确认对话框");
                    var result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                        onConfirm?.Invoke();
                    else
                        onCancel?.Invoke();
                    return;
                }

                // 暂时使用 MessageBox 替代，直到找到正确的 AntdUI 用法
                var dialogResult = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (dialogResult == DialogResult.Yes)
                    onConfirm?.Invoke();
                else
                    onCancel?.Invoke();

                _logger.LogDebug($"显示确认对话框: {title} - {message}");
            }
            catch (Exception ex)
            {
                _logger.LogError("显示确认对话框失败", ex);
                var result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    onConfirm?.Invoke();
                else
                    onCancel?.Invoke();
            }
        }

        public void CloseAllMessages()
        {
            try
            {
                AntdUI.Message.close_all();
                _logger.LogDebug("关闭所有消息");
            }
            catch (Exception ex)
            {
                _logger.LogError("关闭所有消息失败", ex);
            }
        }

        public void CloseAllNotifications()
        {
            try
            {
                AntdUI.Notification.close_all();
                _logger.LogDebug("关闭所有通知");
            }
            catch (Exception ex)
            {
                _logger.LogError("关闭所有通知失败", ex);
            }
        }

        private AntdUI.TType GetAntdIcon(NotificationType type)
        {
            switch (type)
            {
                case NotificationType.Info:
                    return AntdUI.TType.Info;
                case NotificationType.Warning:
                    return AntdUI.TType.Warn;
                case NotificationType.Error:
                    return AntdUI.TType.Error;
                case NotificationType.Success:
                    return AntdUI.TType.Success;
                default:
                    return AntdUI.TType.Info;
            }
        }

        private MessageBoxIcon GetMessageBoxIcon(NotificationType type)
        {
            switch (type)
            {
                case NotificationType.Info:
                    return MessageBoxIcon.Information;
                case NotificationType.Warning:
                    return MessageBoxIcon.Warning;
                case NotificationType.Error:
                    return MessageBoxIcon.Error;
                case NotificationType.Success:
                    return MessageBoxIcon.Information;
                default:
                    return MessageBoxIcon.Information;
            }
        }
    }
}
