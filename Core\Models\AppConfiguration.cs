using System;

namespace TCP通讯.Infrastructure.Configuration
{
    /// <summary>
    /// 应用程序配置模型
    /// </summary>
    public class AppConfiguration
    {
        /// <summary>
        /// 应用程序设置
        /// </summary>
        public ApplicationSettings Application { get; set; } = new ApplicationSettings();

        /// <summary>
        /// UI设置
        /// </summary>
        public UISettings UI { get; set; } = new UISettings();

        /// <summary>
        /// 数据过滤设置
        /// </summary>
        public DataFilterConfiguration DataFilter { get; set; } = new DataFilterConfiguration();

        /// <summary>
        /// 日志设置
        /// </summary>
        public LoggingSettings Logging { get; set; } = new LoggingSettings();
    }

    /// <summary>
    /// 应用程序设置
    /// </summary>
    public class ApplicationSettings
    {
        /// <summary>
        /// 是否开机自启
        /// </summary>
        public bool AutoStart { get; set; } = false;

        /// <summary>
        /// 是否最小化到系统托盘
        /// </summary>
        public bool MinimizeToTray { get; set; } = true;

        /// <summary>
        /// 是否自动连接
        /// </summary>
        public bool AutoConnect { get; set; } = false;

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";
    }

    /// <summary>
    /// UI设置
    /// </summary>
    public class UISettings
    {
        /// <summary>
        /// 皮肤文件路径
        /// </summary>
        public string SkinFile { get; set; } = "PageColor2.ssk";

        /// <summary>
        /// 窗体位置X
        /// </summary>
        public int WindowX { get; set; } = -1;

        /// <summary>
        /// 窗体位置Y
        /// </summary>
        public int WindowY { get; set; } = -1;

        /// <summary>
        /// 窗体宽度
        /// </summary>
        public int WindowWidth { get; set; } = 800;

        /// <summary>
        /// 窗体高度
        /// </summary>
        public int WindowHeight { get; set; } = 600;

        /// <summary>
        /// 是否记住窗体位置
        /// </summary>
        public bool RememberWindowPosition { get; set; } = true;
    }

    /// <summary>
    /// 数据过滤配置
    /// </summary>
    public class DataFilterConfiguration
    {
        /// <summary>
        /// 是否启用重复检查
        /// </summary>
        public bool EnableDuplicateCheck { get; set; } = false;

        /// <summary>
        /// 是否允许重复数据
        /// </summary>
        public bool AllowDuplicates { get; set; } = true;

        /// <summary>
        /// 重复检查时间间隔（分钟）
        /// </summary>
        public int DuplicateCheckInterval { get; set; } = 1;

        /// <summary>
        /// 标签前缀
        /// </summary>
        public string LabelPrefix { get; set; } = "";

        /// <summary>
        /// 单元格前缀
        /// </summary>
        public string CellPrefix { get; set; } = "";

        /// <summary>
        /// PCM前缀
        /// </summary>
        public string PCMPrefix { get; set; } = "";

        /// <summary>
        /// 是否配置已锁定
        /// </summary>
        public bool IsLocked { get; set; } = false;
    }

    /// <summary>
    /// 日志设置
    /// </summary>
    public class LoggingSettings
    {
        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Info";

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "";

        /// <summary>
        /// 日志保留天数
        /// </summary>
        public int RetentionDays { get; set; } = 30;

        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool EnableLogging { get; set; } = true;
    }
}
