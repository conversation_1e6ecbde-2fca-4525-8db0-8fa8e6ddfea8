# TCP通讯程序 v2.0

基于基恩士开发的Socket通讯模块，支持多设备TCP连接和数据处理。

## 🚀 主要功能

### 核心功能
- **多服务器支持** - 同时监听两个不同的IP地址和端口
- **数据过滤** - 支持数据前缀过滤和重复检查
- **键盘输入模拟** - 自动将接收到的数据转换为键盘输入
- **实时状态监控** - 显示连接状态和数据处理情况

### 系统功能
- **开机自启** - 支持设置程序开机自动启动
- **系统托盘** - 最小化到系统托盘，支持托盘操作
- **配置管理** - 统一的JSON配置文件管理
- **日志记录** - 完整的操作日志和错误记录

### 高级功能
- **性能监控** - 实时监控CPU、内存、网络等性能指标
- **数据导出** - 支持导出数据到CSV、Excel、JSON等格式
- **插件系统** - 支持第三方插件扩展功能
- **配置迁移** - 自动从旧版本配置文件迁移

## 📦 安装和使用

### 系统要求
- Windows 7/8/10/11
- .NET Framework 4.7.2 或更高版本
- 管理员权限（用于开机自启功能）

### 快速开始

1. **启动程序**
   - 双击 `Socket通讯.exe` 启动程序
   - 首次启动会自动迁移旧版本配置

2. **配置服务器**
   - 在"服务器配置"区域设置IP地址和端口
   - 支持从下拉列表选择本机IP地址
   - 可以配置一个或两个服务器

3. **开始监听**
   - 点击"开始监听"按钮启动TCP服务器
   - 状态标签会显示当前连接状态
   - 接收到的数据会显示在数据显示区

4. **数据过滤配置**
   - 启用重复检查：防止短时间内重复数据
   - 设置前缀过滤：只处理特定前缀的数据
   - 保存并锁定配置防止误操作

## 🔧 配置说明

### 服务器配置
```
扫码枪1 IP: 监听的IP地址（通常选择本机IP）
扫码枪1 端口: 监听端口号（如8001）
扫码枪2 IP: 第二个服务器的IP地址
扫码枪2 端口: 第二个服务器的端口号（如8002）
```

### 数据过滤配置
```
启用重复检查: 是否检查重复数据
允许重复数据: 是否允许重复数据通过
时间间隔: 重复检查的时间间隔（分钟）
标签前缀: 标签数据的前缀过滤
单元格前缀: 单元格数据的前缀过滤
PCM前缀: PCM数据的前缀过滤
```

### 系统设置
```
设置开机自启: 将程序添加到系统启动项
取消开机自启: 从系统启动项中移除程序
创建桌面快捷方式: 在桌面创建程序快捷方式
```

## 📁 文件结构

```
TCP通讯/
├── Socket通讯.exe          # 主程序
├── Config/                 # 配置文件目录
│   ├── app.json           # 应用程序配置
│   ├── servers.json       # 服务器配置
│   ├── filter.json        # 数据过滤配置
│   └── Backup/            # 配置备份目录
├── Logs/                  # 日志文件目录
│   └── tcp_log_*.txt      # 日志文件
└── Plugins/               # 插件目录（可选）
```

## 🔍 故障排除

### 常见问题

**Q: 程序无法启动**
A: 检查是否安装了.NET Framework 4.7.2，尝试以管理员身份运行

**Q: 无法监听端口**
A: 检查端口是否被其他程序占用，尝试更换端口号

**Q: 数据接收不到**
A: 检查防火墙设置，确保端口未被阻止

**Q: 配置丢失**
A: 检查Config/Backup目录中的备份文件，可以手动恢复

### 日志查看
- 日志文件位于 `Logs/` 目录
- 文件名格式：`tcp_log_YYYYMMDD.txt`
- 包含详细的操作记录和错误信息

### 性能优化
- 定期清理日志文件（程序会自动清理30天前的日志）
- 避免同时处理大量数据
- 合理设置重复检查时间间隔

## 🔄 版本更新

### v2.0 新特性
- 全新的分层架构设计
- 改进的性能和稳定性
- 统一的JSON配置管理
- 完善的日志记录系统
- 新增性能监控功能
- 支持插件扩展
- 开机自启动功能

## 📞 技术支持

- **作者**: 听闻远方有你
- **联系方式**: 1302000857
- **版本**: 2.0.0
- **更新日期**: 2024年

## 📄 许可证

版权所有 © 2024 听闻远方有你

本软件仅供学习和研究使用。
