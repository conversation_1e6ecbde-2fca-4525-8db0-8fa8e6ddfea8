using System;
using System.Diagnostics;
using System.IO;
using System.Security.Principal;
using System.Windows.Forms;
using TCP通讯.Core.Interfaces;
using TCP通讯.Infrastructure.Logging;
using TCP通讯.Common;

namespace TCP通讯.Core.Services
{
    /// <summary>
    /// 系统服务实现
    /// </summary>
    public class SystemService : ISystemService
    {
        private readonly ILogger _logger;
        private readonly string _executablePath;
        private readonly string _applicationName;

        public event EventHandler<SystemEventArgs> SystemEvent;

        public SystemService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _executablePath = Application.ExecutablePath;
            _applicationName = Constants.Application.Name;
        }

        public bool SetAutoStart(bool enable)
        {
            try
            {
                var startupPath = Environment.GetFolderPath(Environment.SpecialFolder.Startup);
                var shortcutPath = Path.Combine(startupPath, $"{_applicationName}.lnk");

                if (enable)
                {
                    // 创建带有自启动参数的快捷方式
                    CreateShortcutFile(shortcutPath, _executablePath,
                        Path.GetDirectoryName(_executablePath),
                        $"{_applicationName}程序快捷方式",
                        "/autostart"); // 添加自启动命令行参数

                    _logger.LogInfo("开机自启已启用");
                    OnSystemEvent(SystemEventType.AutoStartEnabled, "开机自启已启用", true);
                    return true;
                }
                else
                {
                    if (System.IO.File.Exists(shortcutPath))
                    {
                        System.IO.File.Delete(shortcutPath);
                        _logger.LogInfo("开机自启已禁用");
                        OnSystemEvent(SystemEventType.AutoStartDisabled, "开机自启已禁用", true);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("开机自启快捷方式不存在");
                        OnSystemEvent(SystemEventType.AutoStartDisabled, "开机自启未设置", false);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"设置开机自启失败: {enable}", ex);
                OnSystemEvent(enable ? SystemEventType.AutoStartEnabled : SystemEventType.AutoStartDisabled, 
                    ex.Message, false, ex);
                return false;
            }
        }

        public bool IsAutoStartEnabled()
        {
            try
            {
                var startupPath = Environment.GetFolderPath(Environment.SpecialFolder.Startup);
                var shortcutPath = Path.Combine(startupPath, $"{_applicationName}.lnk");
                return System.IO.File.Exists(shortcutPath);
            }
            catch (Exception ex)
            {
                _logger.LogError("检查开机自启状态失败", ex);
                return false;
            }
        }

        public bool CreateDesktopShortcut(string shortcutName = null, string description = null)
        {
            try
            {
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var shortcutPath = Path.Combine(desktopPath, $"{shortcutName ?? _applicationName}.lnk");
                
                CreateShortcutFile(shortcutPath, _executablePath, 
                    Path.GetDirectoryName(_executablePath), 
                    description ?? $"{_applicationName}桌面快捷方式");

                _logger.LogInfo($"桌面快捷方式已创建: {shortcutPath}");
                OnSystemEvent(SystemEventType.ShortcutCreated, "桌面快捷方式已创建", true);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("创建桌面快捷方式失败", ex);
                OnSystemEvent(SystemEventType.ShortcutCreated, ex.Message, false, ex);
                return false;
            }
        }

        public bool RemoveDesktopShortcut(string shortcutName = null)
        {
            try
            {
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var shortcutPath = Path.Combine(desktopPath, $"{shortcutName ?? _applicationName}.lnk");

                if (System.IO.File.Exists(shortcutPath))
                {
                    System.IO.File.Delete(shortcutPath);
                    _logger.LogInfo($"桌面快捷方式已删除: {shortcutPath}");
                    OnSystemEvent(SystemEventType.ShortcutRemoved, "桌面快捷方式已删除", true);
                    return true;
                }
                else
                {
                    _logger.LogWarning("桌面快捷方式不存在");
                    OnSystemEvent(SystemEventType.ShortcutRemoved, "桌面快捷方式不存在", false);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("删除桌面快捷方式失败", ex);
                OnSystemEvent(SystemEventType.ShortcutRemoved, ex.Message, false, ex);
                return false;
            }
        }

        public ApplicationInfo GetApplicationInfo()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return new ApplicationInfo
                {
                    Name = _applicationName,
                    Version = Constants.Application.Version,
                    ExecutablePath = _executablePath,
                    WorkingDirectory = Environment.CurrentDirectory,
                    StartTime = process.StartTime,
                    IsRunningAsAdministrator = IsRunningAsAdministrator(),
                    ProcessId = process.Id.ToString(),
                    MemoryUsage = process.WorkingSet64
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("获取应用程序信息失败", ex);
                return new ApplicationInfo
                {
                    Name = _applicationName,
                    Version = Constants.Application.Version,
                    ExecutablePath = _executablePath
                };
            }
        }

        public bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception ex)
            {
                _logger.LogError("检查管理员权限失败", ex);
                return false;
            }
        }

        public void RestartApplication(bool asAdministrator = false)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = _executablePath,
                    WorkingDirectory = Path.GetDirectoryName(_executablePath),
                    UseShellExecute = true
                };

                if (asAdministrator)
                {
                    startInfo.Verb = "runas";
                }

                Process.Start(startInfo);
                
                _logger.LogInfo($"应用程序重启: 管理员模式={asAdministrator}");
                OnSystemEvent(SystemEventType.ApplicationRestarted, "应用程序正在重启", true);
                
                Application.Exit();
            }
            catch (Exception ex)
            {
                _logger.LogError($"重启应用程序失败: 管理员模式={asAdministrator}", ex);
                OnSystemEvent(SystemEventType.ApplicationRestarted, ex.Message, false, ex);
                throw;
            }
        }

        private void CreateShortcutFile(string shortcutPath, string targetPath, string workingDirectory, string description, string arguments = null)
        {
            try
            {
                // 使用 WScript.Shell 创建真正的 .lnk 快捷方式文件
                Type shellType = Type.GetTypeFromProgID("WScript.Shell");
                dynamic shell = Activator.CreateInstance(shellType);
                var shortcut = shell.CreateShortcut(shortcutPath);
                shortcut.TargetPath = targetPath;
                shortcut.WorkingDirectory = workingDirectory;
                shortcut.Description = description;

                // 设置命令行参数
                if (!string.IsNullOrEmpty(arguments))
                {
                    shortcut.Arguments = arguments;
                }

                shortcut.Save();

                _logger.LogInfo($"创建快捷方式: {shortcutPath} (参数: {arguments ?? "无"})");
            }
            catch (Exception ex)
            {
                _logger.LogError($"创建快捷方式文件失败: {shortcutPath}", ex);
                throw;
            }
        }

        private void OnSystemEvent(SystemEventType eventType, string message, bool success, Exception exception = null)
        {
            SystemEvent?.Invoke(this, new SystemEventArgs
            {
                EventType = eventType,
                Message = message,
                Success = success,
                Exception = exception
            });
        }
    }
}
