using System;
using System.IO;
using System.Net;
using System.Text.Json;
using System.Text;
using TCP通讯.Core.Models;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Infrastructure.Configuration
{
    /// <summary>
    /// 配置管理器实现
    /// </summary>
    public class ConfigurationManager : IConfigurationManager
    {
        private readonly ILogger _logger;
        private readonly string _configDirectory;
        private readonly string _appConfigPath;
        private readonly string _serverConfigPath;
        private readonly string _filterConfigPath;

        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        public ConfigurationManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _configDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
            _appConfigPath = Path.Combine(_configDirectory, "app.json");
            _serverConfigPath = Path.Combine(_configDirectory, "servers.json");
            _filterConfigPath = Path.Combine(_configDirectory, "filter.json");

            EnsureConfigDirectoryExists();
        }

        public AppConfiguration LoadConfiguration()
        {
            try
            {
                if (!File.Exists(_appConfigPath))
                {
                    _logger.LogInfo("应用配置文件不存在，创建默认配置");
                    var defaultConfig = CreateDefaultAppConfiguration();
                    SaveConfiguration(defaultConfig);
                    return defaultConfig;
                }

                var json = File.ReadAllText(_appConfigPath);
                var config = JsonSerializer.Deserialize<AppConfiguration>(json, GetJsonOptions());
                
                _logger.LogInfo("应用配置加载成功");
                return config ?? CreateDefaultAppConfiguration();
            }
            catch (Exception ex)
            {
                _logger.LogError("加载应用配置失败，使用默认配置", ex);
                return CreateDefaultAppConfiguration();
            }
        }

        public void SaveConfiguration(AppConfiguration config)
        {
            try
            {
                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                var json = JsonSerializer.Serialize(config, GetJsonOptions());
                File.WriteAllText(_appConfigPath, json);
                
                OnConfigurationChanged("AppConfiguration", null, config);
                _logger.LogInfo("应用配置保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError("保存应用配置失败", ex);
                throw;
            }
        }

        public TCP通讯.Core.Models.ServerConfiguration[] GetServerConfigurations()
        {
            try
            {
                if (!File.Exists(_serverConfigPath))
                {
                    _logger.LogInfo("服务器配置文件不存在，创建默认配置");
                    var defaultConfigs = CreateDefaultServerConfigurations();
                    SaveServerConfigurations(defaultConfigs);
                    return defaultConfigs;
                }

                var json = File.ReadAllText(_serverConfigPath);
                var configs = JsonSerializer.Deserialize<TCP通讯.Core.Models.ServerConfiguration[]>(json, GetJsonOptions());
                
                _logger.LogInfo($"服务器配置加载成功，共 {configs?.Length ?? 0} 个服务器");
                return configs ?? CreateDefaultServerConfigurations();
            }
            catch (Exception ex)
            {
                _logger.LogError("加载服务器配置失败，使用默认配置", ex);
                return CreateDefaultServerConfigurations();
            }
        }

        public void SaveServerConfigurations(TCP通讯.Core.Models.ServerConfiguration[] configs)
        {
            try
            {
                if (configs == null)
                    throw new ArgumentNullException(nameof(configs));

                var json = JsonSerializer.Serialize(configs, GetJsonOptions());
                File.WriteAllText(_serverConfigPath, json);
                
                OnConfigurationChanged("ServerConfigurations", null, configs);
                _logger.LogInfo($"服务器配置保存成功，共 {configs.Length} 个服务器");
            }
            catch (Exception ex)
            {
                _logger.LogError("保存服务器配置失败", ex);
                throw;
            }
        }

        public DataFilterConfiguration GetDataFilterConfiguration()
        {
            try
            {
                if (!File.Exists(_filterConfigPath))
                {
                    _logger.LogInfo("数据过滤配置文件不存在，创建默认配置");
                    var defaultConfig = CreateDefaultFilterConfiguration();
                    SaveDataFilterConfiguration(defaultConfig);
                    return defaultConfig;
                }

                var json = File.ReadAllText(_filterConfigPath);
                var config = JsonSerializer.Deserialize<DataFilterConfiguration>(json, GetJsonOptions());
                
                _logger.LogInfo("数据过滤配置加载成功");
                return config ?? CreateDefaultFilterConfiguration();
            }
            catch (Exception ex)
            {
                _logger.LogError("加载数据过滤配置失败，使用默认配置", ex);
                return CreateDefaultFilterConfiguration();
            }
        }

        public void SaveDataFilterConfiguration(DataFilterConfiguration config)
        {
            try
            {
                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                var json = JsonSerializer.Serialize(config, GetJsonOptions());
                File.WriteAllText(_filterConfigPath, json);
                
                OnConfigurationChanged("DataFilterConfiguration", null, config);
                _logger.LogInfo("数据过滤配置保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError("保存数据过滤配置失败", ex);
                throw;
            }
        }

        public void ResetToDefault()
        {
            try
            {
                _logger.LogInfo("重置所有配置为默认值");
                
                SaveConfiguration(CreateDefaultAppConfiguration());
                SaveServerConfigurations(CreateDefaultServerConfigurations());
                SaveDataFilterConfiguration(CreateDefaultFilterConfiguration());
                
                _logger.LogInfo("配置重置完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("重置配置失败", ex);
                throw;
            }
        }

        public bool ValidateConfiguration()
        {
            try
            {
                var appConfig = LoadConfiguration();
                var serverConfigs = GetServerConfigurations();
                var filterConfig = GetDataFilterConfiguration();

                // 验证应用配置
                if (appConfig == null)
                    return false;

                // 验证服务器配置
                if (serverConfigs != null)
                {
                    foreach (var config in serverConfigs)
                    {
                        if (!config.IsValid())
                        {
                            _logger.LogWarning($"服务器配置无效: {config}");
                            return false;
                        }
                    }
                }

                // 验证过滤配置
                if (filterConfig == null)
                    return false;

                _logger.LogInfo("配置验证通过");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("配置验证失败", ex);
                return false;
            }
        }

        private void EnsureConfigDirectoryExists()
        {
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
                _logger.LogInfo($"创建配置目录: {_configDirectory}");
            }
        }

        private AppConfiguration CreateDefaultAppConfiguration()
        {
            return new AppConfiguration
            {
                Application = new ApplicationSettings
                {
                    AutoStart = false,
                    MinimizeToTray = true,
                    AutoConnect = false,
                    Version = "2.0.0"
                },
                UI = new UISettings
                {
                    SkinFile = "PageColor2.ssk",
                    WindowWidth = 800,
                    WindowHeight = 600,
                    RememberWindowPosition = true
                },
                DataFilter = CreateDefaultFilterConfiguration(),
                Logging = new LoggingSettings
                {
                    LogLevel = "Info",
                    EnableLogging = true,
                    RetentionDays = 30
                }
            };
        }

        private TCP通讯.Core.Models.ServerConfiguration[] CreateDefaultServerConfigurations()
        {
            return new[]
            {
                new TCP通讯.Core.Models.ServerConfiguration
                {
                    Name = "扫码枪1",
                    IPAddress = IPAddress.Any,
                    Port = 8001,
                    IsEnabled = false
                },
                new TCP通讯.Core.Models.ServerConfiguration
                {
                    Name = "扫码枪2",
                    IPAddress = IPAddress.Any,
                    Port = 8002,
                    IsEnabled = false
                }
            };
        }

        private DataFilterConfiguration CreateDefaultFilterConfiguration()
        {
            return new DataFilterConfiguration
            {
                EnableDuplicateCheck = false,
                AllowDuplicates = true,
                DuplicateCheckInterval = 1,
                LabelPrefix = "",
                CellPrefix = "",
                PCMPrefix = "",
                IsLocked = false
            };
        }

        private JsonSerializerOptions GetJsonOptions()
        {
            return new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new IPAddressJsonConverter() }
            };
        }

        private void OnConfigurationChanged(string configurationType, object oldValue, object newValue)
        {
            ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
            {
                ConfigurationType = configurationType,
                OldValue = oldValue,
                NewValue = newValue
            });
        }
    }

    /// <summary>
    /// IPAddress JSON转换器
    /// </summary>
    public class IPAddressJsonConverter : System.Text.Json.Serialization.JsonConverter<IPAddress>
    {
        public override IPAddress Read(ref System.Text.Json.Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            return string.IsNullOrEmpty(value) ? IPAddress.Any : IPAddress.Parse(value);
        }

        public override void Write(System.Text.Json.Utf8JsonWriter writer, IPAddress value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value?.ToString() ?? IPAddress.Any.ToString());
        }
    }
}
