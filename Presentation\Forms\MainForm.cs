using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using TCP通讯.Core;
using TCP通讯.Core.Interfaces;
using TCP通讯.Core.Models;
using TCP通讯.Core.Services;
using TCP通讯.Infrastructure.Configuration;
using TCP通讯.Common;
using TCP通讯.Presentation.Helpers;

namespace TCP通讯.Presentation.Forms
{
    /// <summary>
    /// 主窗体 - 重构后的版本
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly ApplicationController _appController;
        private readonly ISystemService _systemService;
        private readonly StartupMode _startupMode;
        private AppConfiguration _appConfig;
        private TCP通讯.Core.Models.ServerConfiguration[] _serverConfigs;
        private TCP通讯.Infrastructure.Configuration.DataFilterConfiguration _filterConfig;

        // 通知管理器
        private TrayNotificationManager _notifications;


        // UI控件 - 将在Designer中定义
        private GroupBox groupBoxServers;
        private GroupBox groupBoxDataDisplay;
        private GroupBox groupBoxDataFilter;
        private GroupBox groupBoxSettings;

        private AntdUI.Select comboBoxServer1IP;
        private AntdUI.Input textBoxServer1Port;
        private AntdUI.Select comboBoxServer2IP;
        private AntdUI.Input textBoxServer2Port;

        private AntdUI.Button buttonStart;
        private AntdUI.Button buttonStop;
        private AntdUI.Button buttonClear;

        private Label labelServer1Status;
        private Label labelServer2Status;

        private AntdUI.Input textBoxDataDisplay;
        private Label labelFilterStatus;

        private CheckBox checkBoxEnableDuplicateCheck;
        private CheckBox checkBoxAllowDuplicates;
        private AntdUI.Select comboBoxDuplicateInterval;

        private AntdUI.Input textBoxLabelPrefix;
        private AntdUI.Input textBoxCellPrefix;
        private AntdUI.Input textBoxPCMPrefix;

        private AntdUI.Button buttonSaveFilter;
        private AntdUI.Button buttonUnlockFilter;

        private NotifyIcon notifyIcon;
        private ContextMenuStrip contextMenuStrip;

        // 设置相关控件
        private AntdUI.Button buttonAutoStart;
        private AntdUI.Button buttonCancelAutoStart;
        private AntdUI.Button buttonCreateShortcut;
        private AntdUI.Button buttonAbout;



        public MainForm(ApplicationController appController, StartupMode startupMode = StartupMode.Manual)
        {
            _appController = appController ?? throw new ArgumentNullException(nameof(appController));
            _systemService = new SystemService(_appController.Logger);
            _startupMode = startupMode;

            InitializeComponent();
            InitializeCustomComponents();

            LoadConfiguration();
            SubscribeToEvents();

            // 初始化通知管理器
            _notifications = new TrayNotificationManager(this);

            // 设置持久通知间隔为30秒（可根据需要调整）
            _notifications.SetPersistentNotificationInterval(30);

            // 设置消息服务的默认窗体
            _appController.SetMessageDefaultForm(this);

            // 处理开机自启动逻辑
            HandleAutoStartupBehavior();

            // 确保UI状态与应用程序状态同步
            EnsureUIStateSynchronization();
        }

        /// <summary>
        /// 确保UI状态与应用程序状态同步
        /// </summary>
        private void EnsureUIStateSynchronization()
        {
            // 延迟检查，确保所有组件都已初始化
            _ = Task.Delay(1000).ContinueWith(t =>
            {
                this.Invoke(new Action(() =>
                {
                    // 同步UI状态与应用程序实际状态
                    UpdateUIForRunningState(_appController.IsRunning);
                    _appController.Logger.LogInfo($"UI状态已同步，应用程序运行状态: {_appController.IsRunning}");
                }));
            });
        }

        private void InitializeCustomComponents()
        {
            // 设置窗体属性
            this.Text = $"{Constants.Application.Name} v{Constants.Application.Version}";
            this.Size = new Size(_appConfig?.UI?.WindowWidth ?? Constants.UI.DefaultWindowWidth,
                               _appConfig?.UI?.WindowHeight ?? Constants.UI.DefaultWindowHeight);
            this.MinimumSize = new Size(Constants.UI.MinWindowWidth, Constants.UI.MinWindowHeight);
            this.StartPosition = FormStartPosition.CenterScreen;
            
            // 设置窗体图标
            try
            {
                // 暂时注释掉图标设置，避免Properties.Resources引用问题
                // this.Icon = Properties.Resources.基恩士;
            }
            catch
            {
                // 忽略图标加载错误
            }

            // 初始化系统托盘
            InitializeNotifyIcon();
            
            // 加载本地IP地址
            LoadLocalIPAddresses();
            
            // 初始化数据显示区域
            InitializeDataDisplay();

            // 初始化下拉框
            InitializeComboBoxes();

            // 初始化状态标签
            UpdateConnectionStatus(labelServer1Status, ConnectionStatus.Waiting.ToString());
            UpdateConnectionStatus(labelServer2Status, ConnectionStatus.Waiting.ToString());
        }

        private void InitializeNotifyIcon()
        {
            notifyIcon = new NotifyIcon();
            notifyIcon.Icon = this.Icon;
            notifyIcon.Text = this.Text;
            notifyIcon.Visible = false;

            // 创建右键菜单
            contextMenuStrip = new ContextMenuStrip();

            var showMenuItem = new ToolStripMenuItem("显示主窗口");
            showMenuItem.Click += OnTrayShowMenuClick;
            contextMenuStrip.Items.Add(showMenuItem);
            contextMenuStrip.Items.Add("-");

            // 添加启动模式信息
            var startupModeText = _startupMode == StartupMode.AutoStart ? "开机自启动模式" : "手动启动模式";
            var modeMenuItem = new ToolStripMenuItem(startupModeText);
            modeMenuItem.Enabled = false; // 只显示信息，不可点击
            contextMenuStrip.Items.Add(modeMenuItem);
            contextMenuStrip.Items.Add("-");

            // 添加服务器控制菜单
            var startMenuItem = new ToolStripMenuItem("开始监听");
            startMenuItem.Click += OnTrayStartMenuClick;
            contextMenuStrip.Items.Add(startMenuItem);

            var stopMenuItem = new ToolStripMenuItem("停止监听");
            stopMenuItem.Click += OnTrayStopMenuClick;
            contextMenuStrip.Items.Add(stopMenuItem);

            contextMenuStrip.Items.Add("-");

            var aboutMenuItem = new ToolStripMenuItem("关于");
            aboutMenuItem.Click += OnTrayAboutMenuClick;
            contextMenuStrip.Items.Add(aboutMenuItem);

            var exitMenuItem = new ToolStripMenuItem("退出");
            exitMenuItem.Click += OnTrayExitMenuClick;
            contextMenuStrip.Items.Add(exitMenuItem);

            notifyIcon.ContextMenuStrip = contextMenuStrip;
            notifyIcon.DoubleClick += OnTrayDoubleClick;

            // 订阅系统服务事件
            _systemService.SystemEvent += OnSystemEvent;
        }

        /// <summary>
        /// 处理开机自启动行为
        /// </summary>
        private void HandleAutoStartupBehavior()
        {
            if (_startupMode == StartupMode.AutoStart)
            {
                _appController.Logger.LogInfo("检测到开机自启动模式，执行自启动逻辑");

                // 开机自启动时直接最小化到托盘
                this.WindowState = FormWindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Hide();
                notifyIcon.Visible = true;

                // 延迟启动TCP监听服务，确保系统完全启动
                _ = Task.Delay(3000).ContinueWith(async t =>
                {
                    try
                    {
                        await StartTcpListeningOnAutoStart();
                    }
                    catch (Exception ex)
                    {
                        _appController.Logger.LogError("开机自启动TCP监听失败", ex);

                        // 显示托盘通知
                        this.Invoke(new Action(() =>
                        {
                            notifyIcon?.ShowBalloonTip(5000, "TCP通讯",
                                $"自动启动TCP监听失败: {ex.Message}", ToolTipIcon.Error);
                        }));
                    }
                });
            }
        }

        /// <summary>
        /// 开机自启动时启动TCP监听服务
        /// </summary>
        private async Task StartTcpListeningOnAutoStart()
        {
            // 检查是否有启用的服务器配置
            if (_serverConfigs?.Any(s => s.IsEnabled) == true)
            {
                _appController.Logger.LogInfo("开机自启动：开始启动TCP监听服务");

                // 启动TCP监听服务
                await _appController.StartAsync();

                // 显示成功通知并更新UI状态
                this.Invoke(new Action(() =>
                {
                    // 更新UI状态为运行状态
                    UpdateUIForRunningState(true);

                    // 使用托盘气泡通知
                    notifyIcon?.ShowBalloonTip(3000, "TCP通讯",
                        "TCP监听已开启", ToolTipIcon.Info);

                    // 同时使用AntdUI通知（如果主窗体可见）
                    try
                    {
                        AntdUI.Message.success(this, "TCP监听已自动开启", null, 3);
                    }
                    catch
                    {
                        // 如果AntdUI通知失败，忽略错误
                    }
                }));

                _appController.Logger.LogInfo("开机自启动：TCP监听服务启动成功");
            }
            else
            {
                _appController.Logger.LogWarning("开机自启动：没有启用的服务器配置，跳过TCP监听启动");

                // 显示警告通知
                this.Invoke(new Action(() =>
                {
                    notifyIcon?.ShowBalloonTip(3000, "TCP通讯",
                        "没有启用的服务器配置", ToolTipIcon.Warning);
                }));
            }
        }

        private void InitializeDataDisplay()
        {
            if (textBoxDataDisplay != null)
            {
                textBoxDataDisplay.Multiline = true;
                textBoxDataDisplay.ReadOnly = true;
                // AntdUI.Input 不支持 ScrollBars, WordWrap, MaxLength 属性
                // 这些功能由控件内部自动处理
            }
        }

        private void LoadConfiguration()
        {
            try
            {
                _appConfig = _appController.Configuration;
                _serverConfigs = _appController.GetServerConfigurations();
                _filterConfig = _appController.GetDataFilterConfiguration();
                
                ApplyConfigurationToUI();
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"加载配置失败: {ex.Message}", this);
            }
        }

        private void ApplyConfigurationToUI()
        {
            if (_serverConfigs?.Length >= 2)
            {
                // 服务器1配置
                var server1 = _serverConfigs[0];
                if (comboBoxServer1IP != null)
                    comboBoxServer1IP.Text = server1.IPAddress?.ToString() ?? "";
                if (textBoxServer1Port != null)
                    textBoxServer1Port.Text = server1.Port.ToString();

                // 服务器2配置
                var server2 = _serverConfigs[1];
                if (comboBoxServer2IP != null)
                    comboBoxServer2IP.Text = server2.IPAddress?.ToString() ?? "";
                if (textBoxServer2Port != null)
                    textBoxServer2Port.Text = server2.Port.ToString();
            }

            if (_filterConfig != null)
            {
                // 数据过滤配置
                if (checkBoxEnableDuplicateCheck != null)
                    checkBoxEnableDuplicateCheck.Checked = _filterConfig.EnableDuplicateCheck;
                if (checkBoxAllowDuplicates != null)
                    checkBoxAllowDuplicates.Checked = _filterConfig.AllowDuplicates;
                if (comboBoxDuplicateInterval != null)
                    comboBoxDuplicateInterval.Text = _filterConfig.DuplicateCheckInterval.ToString();
                
                if (textBoxLabelPrefix != null)
                    textBoxLabelPrefix.Text = _filterConfig.LabelPrefix ?? "";
                if (textBoxCellPrefix != null)
                    textBoxCellPrefix.Text = _filterConfig.CellPrefix ?? "";
                if (textBoxPCMPrefix != null)
                    textBoxPCMPrefix.Text = _filterConfig.PCMPrefix ?? "";

                // 锁定状态
                UpdateFilterLockState(_filterConfig.IsLocked);
            }
        }

        private void LoadLocalIPAddresses()
        {
            try
            {
                var ipAddresses = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces()
                    .SelectMany(ni => ni.GetIPProperties().UnicastAddresses)
                    .Where(addr => addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    .Select(addr => addr.Address.ToString())
                    .ToList();

                // 更新 AntdUI.Select 控件
                if (comboBoxServer1IP != null)
                {
                    var items1 = new AntdUI.BaseCollection();
                    foreach (var ip in ipAddresses)
                    {
                        items1.Add(ip);
                    }
                    comboBoxServer1IP.Items = items1;
                }

                if (comboBoxServer2IP != null)
                {
                    var items2 = new AntdUI.BaseCollection();
                    foreach (var ip in ipAddresses)
                    {
                        items2.Add(ip);
                    }
                    comboBoxServer2IP.Items = items2;
                }
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowWarning($"加载本地IP地址失败: {ex.Message}", this);
            }
        }

        private void InitializeComboBoxes()
        {
            // 初始化时间间隔下拉框
            if (comboBoxDuplicateInterval != null)
            {
                var items = new AntdUI.BaseCollection();
                for (int i = 1; i <= 60; i++)
                {
                    items.Add(i.ToString());
                }
                comboBoxDuplicateInterval.Items = items;
                comboBoxDuplicateInterval.Text = "1";
            }
        }

        private void SubscribeToEvents()
        {
            // 应用程序控制器事件
            _appController.ConnectionStatusChanged += OnConnectionStatusChanged;
            _appController.DataProcessed += OnDataProcessed;
            _appController.DataFiltered += OnDataFiltered;
            _appController.ApplicationError += OnApplicationError;

            // 窗体事件
            this.FormClosing += OnFormClosing;
            this.Resize += OnFormResize;
            this.Load += OnFormLoad;
        }

        private void OnConnectionStatusChanged(object sender, ConnectionStatusEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnConnectionStatusChanged(sender, e)));
                return;
            }

            var statusLabel = e.ServerName == Constants.UI.Server1StatusLabel ? 
                labelServer1Status : labelServer2Status;
            
            if (statusLabel != null)
            {
                UpdateConnectionStatus(statusLabel, e.Status.ToString());
            }
        }

        private void OnDataProcessed(object sender, ProcessedDataEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnDataProcessed(sender, e)));
                return;
            }

            // 更新 AntdUI.Input 控件
            if (textBoxDataDisplay != null)
            {
                textBoxDataDisplay.Text += $"[{e.ProcessedTime:HH:mm:ss}] {e.ProcessedData}\r\n";
                // AntdUI.Input 会自动滚动到底部
            }

            // 清空过滤状态提示
            if (labelFilterStatus != null)
            {
                labelFilterStatus.Text = "";
                labelFilterStatus.ForeColor = Color.Black;
            }
        }

        private void OnDataFiltered(object sender, DataFilteredEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnDataFiltered(sender, e)));
                return;
            }

            if (labelFilterStatus != null)
            {
                if (e.RemainingTime > TimeSpan.Zero)
                {
                    var minutes = Math.Floor(e.RemainingTime.TotalMinutes);
                    var seconds = Math.Floor(e.RemainingTime.TotalSeconds % 60);
                    labelFilterStatus.Text = $"数据已被标记，允许输出剩余时间：{minutes}分{seconds}秒";
                }
                else
                {
                    labelFilterStatus.Text = $"数据被过滤：{e.Reason}";
                }
                labelFilterStatus.ForeColor = Color.Red;
            }
        }

        private void OnApplicationError(object sender, ApplicationErrorEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnApplicationError(sender, e)));
                return;
            }

            _appController.MessageService.ShowError($"应用程序错误：{e.Message}", this);
        }

        private void UpdateConnectionStatus(Label label, string status)
        {
            if (label == null) return;

            switch (status)
            {
                case "Connected":
                case Constants.ConnectionStatus.Connected:
                    label.Text = Constants.ConnectionStatus.Connected;
                    label.BackColor = Color.Green;
                    label.ForeColor = Color.White;
                    break;
                case "Disconnected":
                case Constants.ConnectionStatus.Disconnected:
                    label.Text = Constants.ConnectionStatus.Disconnected;
                    label.BackColor = Color.Red;
                    label.ForeColor = Color.White;
                    break;
                case "Waiting":
                case Constants.ConnectionStatus.Waiting:
                    label.Text = Constants.ConnectionStatus.Waiting;
                    label.BackColor = Color.Yellow;
                    label.ForeColor = Color.Black;
                    break;
                case "Error":
                case Constants.ConnectionStatus.Error:
                    label.Text = Constants.ConnectionStatus.Error;
                    label.BackColor = Color.DarkRed;
                    label.ForeColor = Color.White;
                    break;
                default:
                    label.Text = status;
                    label.BackColor = SystemColors.Control;
                    label.ForeColor = SystemColors.ControlText;
                    break;
            }
        }

        private void UpdateFilterLockState(bool isLocked)
        {
            if (checkBoxEnableDuplicateCheck != null)
                checkBoxEnableDuplicateCheck.Enabled = !isLocked;
            if (checkBoxAllowDuplicates != null)
                checkBoxAllowDuplicates.Enabled = !isLocked;
            if (comboBoxDuplicateInterval != null)
                comboBoxDuplicateInterval.Enabled = !isLocked;
            if (textBoxLabelPrefix != null)
                textBoxLabelPrefix.Enabled = !isLocked;
            if (textBoxCellPrefix != null)
                textBoxCellPrefix.Enabled = !isLocked;
            if (textBoxPCMPrefix != null)
                textBoxPCMPrefix.Enabled = !isLocked;
            
            if (buttonSaveFilter != null)
                buttonSaveFilter.Enabled = !isLocked;
            if (buttonUnlockFilter != null)
                buttonUnlockFilter.Enabled = isLocked;
        }

        private void ShowMainWindow()
        {
            this.Show();
            this.WindowState = FormWindowState.Normal;
            this.Activate();
            notifyIcon.Visible = false;

            // 确保UI状态与应用程序状态同步
            UpdateUIForRunningState(_appController.IsRunning);
            _appController.Logger.LogInfo($"主窗体已显示，UI状态已同步，应用程序运行状态: {_appController.IsRunning}");
        }

        private void ShowAboutDialog()
        {
            try
            {
                // 优先使用 Modal 对话框
                ShowAntdUIModalDialog();

                // 记录日志
                _appController?.Logger?.LogInfo("显示关于模态对话框");
            }
            catch (Exception ex)
            {
                // 如果 Modal 失败，尝试 Message 方式
                _appController?.Logger?.LogWarning("Modal 对话框失败，尝试 Message 方式");
                _appController?.Logger?.LogError("Modal 对话框详细错误", ex);
                try
                {
                    ShowAntdUIAboutMessageAdvanced();
                }
                catch (Exception ex2)
                {
                    // 如果 AntdUI 完全失败，回退到标准消息框
                    _appController?.Logger?.LogError("AntdUI 完全失败，使用标准消息框", ex2);
                    ShowFallbackAboutDialog();
                }
            }
        }

        /// <summary>
        /// 使用 AntdUI.Message 显示关于信息
        /// </summary>
        private void ShowAntdUIAboutMessage()
        {
            var aboutText = $"应用程序：{Constants.Application.Name}\n\n" +
                           $"描述：{Constants.Application.Description}\n" +
                           $"版本：{Constants.Application.Version}\n\n" +
                           $"开发者：{Constants.Application.Author}\n" +
                           $"联系方式：{Constants.Application.Contact}\n\n" +
                           $"{Constants.Application.Copyright}";

            // 使用 AntdUI.Message.info 显示关于信息
            // 参数顺序：Form, Text, Font, AutoClose
            AntdUI.Message.info(this, aboutText, null, 8);
        }

        /// <summary>
        /// 使用 AntdUI.Message.Config 显示关于信息（更高级的配置）
        /// </summary>
        private void ShowAntdUIAboutMessageAdvanced()
        {
            var aboutText = $"应用程序：{Constants.Application.Name}\n\n" +
                           $"描述：{Constants.Application.Description}\n" +
                           $"版本：{Constants.Application.Version}\n\n" +
                           $"开发者：{Constants.Application.Author}\n" +
                           $"联系方式：{Constants.Application.Contact}\n\n" +
                           $"{Constants.Application.Copyright}";

            // 根据文档，Message.Config 需要必需的参数
            // 使用简化的方式，直接调用 Message.info
            AntdUI.Message.info(this, aboutText, null, 10);
        }

        /// <summary>
        /// 使用 AntdUI Modal 显示关于对话框（主要实现）
        /// </summary>
        private void ShowAntdUIModalDialog()
        {
            try
            {
                // 创建关于对话框的内容
                var aboutContent = CreateAboutDialogContent();

                // 使用 AntdUI Modal 显示关于对话框
                var config = new AntdUI.Modal.Config(this, "关于 " + Constants.Application.Name, aboutContent)
                {
                    Width = 500,
                    Icon = AntdUI.TType.Info,
                    OkText = "确定",
                    CancelText = null, // 不显示取消按钮
                    MaskClosable = true, // 允许点击遮罩关闭
                    Keyboard = true, // 允许 ESC 键关闭
                    Draggable = true, // 允许拖拽
                    CloseIcon = false // 不显示右上角关闭图标
                };

                // 设置确定按钮回调
                config.OnOk = (modalConfig) =>
                {
                    _appController?.Logger?.LogInfo("用户点击确定关闭关于对话框");
                    return true; // 返回 true 关闭对话框
                };

                AntdUI.Modal.open(config);
                _appController?.Logger?.LogInfo("成功打开 AntdUI Modal 关于对话框");
            }
            catch (Exception ex)
            {
                _appController?.Logger?.LogError("AntdUI Modal 显示失败", ex);
                throw; // 重新抛出异常，让上层处理
            }
        }

        /// <summary>
        /// 备用的关于对话框显示方法
        /// </summary>
        private void ShowFallbackAboutDialog()
        {
            var aboutText = $"应用程序：{Constants.Application.Name}\n\n" +
                           $"描述：{Constants.Application.Description}\n" +
                           $"版本：{Constants.Application.Version}\n\n" +
                           $"开发者：{Constants.Application.Author}\n" +
                           $"联系方式：{Constants.Application.Contact}\n\n" +
                           $"{Constants.Application.Copyright}";

            MessageBox.Show(aboutText, "关于 " + Constants.Application.Name,
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 创建关于对话框的内容控件
        /// </summary>
        /// <returns>包含关于信息的控件</returns>
        private Control CreateAboutDialogContent()
        {
            // 使用 AntdUI.Panel 作为主容器
            var mainPanel = new AntdUI.Panel
            {
                Size = new System.Drawing.Size(460, 300),
                BackColor = System.Drawing.Color.White,
                BorderWidth = 0,
                Padding = new Padding(20)
            };

            // 顶部装饰条
            var topBar = new AntdUI.Panel
            {
                Location = new System.Drawing.Point(0, 0),
                Size = new System.Drawing.Size(460, 4),
                BackColor = System.Drawing.Color.FromArgb(24, 144, 255),
                BorderWidth = 0
            };
            mainPanel.Controls.Add(topBar);

            // 应用程序图标区域（使用文字图标）
            var iconLabel = new AntdUI.Label
            {
                Text = "TCP",
                Font = new System.Drawing.Font("Microsoft YaHei", 24, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.FromArgb(24, 144, 255),
                Location = new System.Drawing.Point(0, 25),
                Size = new System.Drawing.Size(460, 45),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(iconLabel);

            // 应用程序标题
            var titleLabel = new AntdUI.Label
            {
                Text = Constants.Application.Name,
                Font = new System.Drawing.Font("Microsoft YaHei", 20, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.FromArgb(24, 144, 255),
                Location = new System.Drawing.Point(0, 80),
                Size = new System.Drawing.Size(460, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(titleLabel);

            // 版本信息
            var versionLabel = new AntdUI.Label
            {
                Text = $"版本 {Constants.Application.Version}",
                Font = new System.Drawing.Font("Microsoft YaHei", 12),
                ForeColor = System.Drawing.Color.FromArgb(102, 102, 102),
                Location = new System.Drawing.Point(0, 125),
                Size = new System.Drawing.Size(460, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(versionLabel);

            // 应用程序描述
            var descriptionLabel = new AntdUI.Label
            {
                Text = Constants.Application.Description,
                Font = new System.Drawing.Font("Microsoft YaHei", 11),
                ForeColor = System.Drawing.Color.FromArgb(64, 64, 64),
                Location = new System.Drawing.Point(30, 155),
                Size = new System.Drawing.Size(400, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(descriptionLabel);

            // 创建信息卡片
            var infoCard = new AntdUI.Panel
            {
                Location = new System.Drawing.Point(60, 195),
                Size = new System.Drawing.Size(340, 75),
                BackColor = System.Drawing.Color.FromArgb(248, 250, 252),
                BorderWidth = 1,
                BorderColor = System.Drawing.Color.FromArgb(230, 230, 230),
                Radius = 10
            };

            // 开发者信息
            var authorLabel = new AntdUI.Label
            {
                Text = $"开发者：{Constants.Application.Author}",
                Font = new System.Drawing.Font("Microsoft YaHei", 10),
                ForeColor = System.Drawing.Color.FromArgb(64, 64, 64),
                Location = new System.Drawing.Point(25, 18),
                Size = new System.Drawing.Size(290, 22),
                TextAlign = ContentAlignment.MiddleLeft
            };
            infoCard.Controls.Add(authorLabel);

            // 联系方式
            var contactLabel = new AntdUI.Label
            {
                Text = $"联系方式：{Constants.Application.Contact}",
                Font = new System.Drawing.Font("Microsoft YaHei", 10),
                ForeColor = System.Drawing.Color.FromArgb(64, 64, 64),
                Location = new System.Drawing.Point(25, 45),
                Size = new System.Drawing.Size(290, 22),
                TextAlign = ContentAlignment.MiddleLeft
            };
            infoCard.Controls.Add(contactLabel);

            mainPanel.Controls.Add(infoCard);

            // 版权信息
            var copyrightLabel = new AntdUI.Label
            {
                Text = Constants.Application.Copyright,
                Font = new System.Drawing.Font("Microsoft YaHei", 9),
                ForeColor = System.Drawing.Color.FromArgb(153, 153, 153),
                Location = new System.Drawing.Point(0, 280),
                Size = new System.Drawing.Size(460, 18),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(copyrightLabel);

            return mainPanel;
        }

        private void ExitApplication()
        {
            Application.Exit();
        }

        private async System.Threading.Tasks.Task StartServersFromTray()
        {
            try
            {
                if (_appController.IsRunning)
                {
                    // 如果已在运行，显示提示
                    _notifications.Info("服务器已在运行中");
                    return;
                }

                // 确保UI操作在主线程中执行
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateServerConfigurationsFromUI()));
                }
                else
                {
                    UpdateServerConfigurationsFromUI();
                }

                // 启动服务器
                await _appController.StartAsync();

                // 更新UI状态 - 这是关键的缺失步骤！
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateUIForRunningState(true)));
                }
                else
                {
                    UpdateUIForRunningState(true);
                }

                // 显示成功通知
                _notifications.Success("监听已开启");
            }
            catch (Exception ex)
            {
                // 显示错误通知
                _notifications.Error($"启动失败: {ex.Message}", autoClose: 5);
            }
        }

        private async System.Threading.Tasks.Task StopServersFromTray()
        {
            try
            {
                if (!_appController.IsRunning)
                {
                    // 如果未在运行，显示提示
                    _notifications.Info("服务器未在运行中");
                    return;
                }

                // 停止服务器
                await _appController.StopAsync();

                // 更新UI状态 - 这是关键的缺失步骤！
                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateUIForRunningState(false)));
                }
                else
                {
                    UpdateUIForRunningState(false);
                }

                // 显示成功通知
                _notifications.Success("监听已停止");
            }
            catch (Exception ex)
            {
                // 显示错误通知
                _notifications.Error($"停止失败: {ex.Message}", autoClose: 5);
            }
        }

        // 托盘菜单事件处理器
        private void OnTrayStartMenuClick(object sender, EventArgs e)
        {
            // 使用 Task.Run 来避免阻塞UI线程
            _ = Task.Run(async () =>
            {
                try
                {
                    await StartServersFromTray();
                }
                catch (Exception ex)
                {
                    // 确保在UI线程中显示错误
                    try
                    {
                        _notifications.Error($"启动失败: {ex.Message}", autoClose: 5);
                    }
                    catch
                    {
                        // 忽略UI更新错误
                        System.Diagnostics.Debug.WriteLine($"托盘启动菜单UI错误: {ex.Message}");
                    }
                }
            });
        }

        private void OnTrayStopMenuClick(object sender, EventArgs e)
        {
            // 使用 Task.Run 来避免阻塞UI线程
            _ = Task.Run(async () =>
            {
                try
                {
                    await StopServersFromTray();
                }
                catch (Exception ex)
                {
                    // 确保在UI线程中显示错误
                    try
                    {
                        _notifications.Error($"停止失败: {ex.Message}", autoClose: 5);
                    }
                    catch
                    {
                        // 忽略UI更新错误
                        System.Diagnostics.Debug.WriteLine($"托盘停止菜单UI错误: {ex.Message}");
                    }
                }
            });
        }

        private void OnTrayAboutMenuClick(object sender, EventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ShowAboutDialog()));
                }
                else
                {
                    ShowAboutDialog();
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不显示给用户，因为这是一个简单的UI操作
                System.Diagnostics.Debug.WriteLine($"托盘关于菜单错误: {ex.Message}");
            }
        }

        private void OnTrayExitMenuClick(object sender, EventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ExitApplication()));
                }
                else
                {
                    ExitApplication();
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不显示给用户
                System.Diagnostics.Debug.WriteLine($"托盘退出菜单错误: {ex.Message}");
            }
        }

        private void OnTrayShowMenuClick(object sender, EventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ShowMainWindow()));
                }
                else
                {
                    ShowMainWindow();
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不显示给用户
                System.Diagnostics.Debug.WriteLine($"托盘显示主窗口错误: {ex.Message}");
            }
        }

        private void OnTrayDoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ShowMainWindow()));
                }
                else
                {
                    ShowMainWindow();
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不显示给用户
                System.Diagnostics.Debug.WriteLine($"托盘双击错误: {ex.Message}");
            }
        }

        private void OnSystemEvent(object sender, SystemEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnSystemEvent(sender, e)));
                return;
            }

            var icon = e.Success ? ToolTipIcon.Info : ToolTipIcon.Warning;
            notifyIcon?.ShowBalloonTip(3000, "系统设置", e.Message, icon);
        }

        #region Event Handlers

        private void OnFormLoad(object sender, EventArgs e)
        {
            // 如果是开机自启动模式，跳过常规的自动连接逻辑
            if (_startupMode == StartupMode.AutoStart)
            {
                _appController.Logger.LogInfo("开机自启动模式，跳过常规自动连接逻辑");
                return;
            }

            // 检查是否需要自动连接（仅在手动启动时）
            if (_appConfig?.Application?.AutoConnect == true &&
                _serverConfigs?.Any(s => s.IsEnabled) == true)
            {
                this.WindowState = FormWindowState.Minimized;
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _appController.StartAsync();
                        if (_appConfig.Application.MinimizeToTray)
                        {
                            this.Invoke(new Action(() =>
                            {
                                this.Hide();
                                notifyIcon.Visible = true;
                            }));
                        }
                    }
                    catch (Exception ex)
                    {
                        this.Invoke(new Action(() =>
                        {
                            _appController.MessageService.ShowError($"自动启动失败: {ex.Message}", this);
                        }));
                    }
                });
            }
            else
            {
                // 如果没有自动连接，检查初始状态并开始持久通知
                _ = Task.Delay(2000).ContinueWith(t =>
                {
                    // 延迟2秒后检查状态，确保所有组件都已初始化
                    if (!_appController.IsRunning)
                    {
                        this.Invoke(new Action(() =>
                        {
                            _notifications?.StartPersistentTcpWarning();
                        }));
                    }
                });
            }
        }

        private void OnFormResize(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized && 
                _appConfig?.Application?.MinimizeToTray == true)
            {
                this.Hide();
                notifyIcon.Visible = true;
            }
        }

        private void OnFormClosing(object sender, FormClosingEventArgs e)
        {
            // 使用AntdUI确认对话框
            e.Cancel = true; // 先取消关闭

            _appController.MessageService.ShowConfirm(
                "退出确认",
                "确定要退出TCP通讯吗？",
                onConfirm: () => {
                    // 确认退出
                    this.Invoke(new Action(() => {
                        // 清理通知管理器资源
                        _notifications?.StopPersistentTcpWarning();
                        _notifications?.Dispose();

                        this.FormClosing -= OnFormClosing; // 移除事件处理器避免递归
                        Application.Exit();
                    }));
                },
                onCancel: () => {
                    // 取消退出，什么都不做
                },
                this
            );

            // 保存窗体位置和大小
            if (_appConfig?.UI?.RememberWindowPosition == true && 
                this.WindowState == FormWindowState.Normal)
            {
                _appConfig.UI.WindowX = this.Location.X;
                _appConfig.UI.WindowY = this.Location.Y;
                _appConfig.UI.WindowWidth = this.Size.Width;
                _appConfig.UI.WindowHeight = this.Size.Height;
                
                try
                {
                    _appController.SaveConfiguration();
                }
                catch
                {
                    // 忽略保存错误
                }
            }
        }

        #endregion

        #region Button Event Handlers

        private async void OnStartButtonClick(object sender, EventArgs e)
        {
            try
            {
                if (_appController.IsRunning)
                {
                    _appController.MessageService.ShowInfo("服务器已在运行中", this);
                    return;
                }

                // 更新服务器配置
                UpdateServerConfigurationsFromUI();

                // 启动服务器
                await _appController.StartAsync();

                // 更新UI状态
                UpdateUIForRunningState(true);

                // 显示成功提示
                _appController.MessageService.ShowSuccess("监听已开启", this);
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"启动服务器失败: {ex.Message}", this);
            }
        }

        private async void OnStopButtonClick(object sender, EventArgs e)
        {
            try
            {
                if (!_appController.IsRunning)
                {
                    _appController.MessageService.ShowInfo("服务器未在运行中", this);
                    return;
                }

                await _appController.StopAsync();

                // 更新UI状态
                UpdateUIForRunningState(false);

                // 显示成功提示
                _appController.MessageService.ShowSuccess("监听已停止", this);
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"停止服务器失败: {ex.Message}", this);
            }
        }

        private void OnClearButtonClick(object sender, EventArgs e)
        {
            // 清空 AntdUI.Input 控件
            if (textBoxDataDisplay != null)
            {
                textBoxDataDisplay.Text = "";
            }

            _appController.MessageService.ShowInfo("数据显示区已清空", this);
        }

        private void OnSaveFilterButtonClick(object sender, EventArgs e)
        {
            try
            {
                UpdateFilterConfigurationFromUI();
                _filterConfig.IsLocked = true;
                _appController.UpdateDataFilterConfiguration(_filterConfig);

                UpdateFilterLockState(true);
                _appController.MessageService.ShowSuccess(Constants.SuccessMessages.ConfigurationSaved, this);
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"保存配置失败: {ex.Message}", this);
            }
        }

        private void OnUnlockFilterButtonClick(object sender, EventArgs e)
        {
            _filterConfig.IsLocked = false;
            UpdateFilterLockState(false);
            _appController.MessageService.ShowSuccess("配置已解锁", this);
        }

        private void OnAutoStartButtonClick(object sender, EventArgs e)
        {
            try
            {
                var success = _systemService.SetAutoStart(true);
                if (success)
                {
                    _appController.MessageService.ShowSuccess(Constants.SuccessMessages.AutoStartEnabled, this);
                }
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"设置开机自启失败: {ex.Message}", this);
            }
        }

        private void OnCancelAutoStartButtonClick(object sender, EventArgs e)
        {
            try
            {
                var success = _systemService.SetAutoStart(false);
                if (success)
                {
                    _appController.MessageService.ShowSuccess(Constants.SuccessMessages.AutoStartDisabled, this);
                }
                else
                {
                    _appController.MessageService.ShowWarning(Constants.SuccessMessages.AutoStartNotSet, this);
                }
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"取消开机自启失败: {ex.Message}", this);
            }
        }

        private void OnCreateShortcutButtonClick(object sender, EventArgs e)
        {
            try
            {
                var success = _systemService.CreateDesktopShortcut();
                if (success)
                {
                    _appController.MessageService.ShowSuccess("桌面快捷方式创建成功", this);
                }
            }
            catch (Exception ex)
            {
                _appController.MessageService.ShowError($"创建桌面快捷方式失败: {ex.Message}", this);
            }
        }

        private void OnAboutButtonClick(object sender, EventArgs e)
        {
            ShowAboutDialog();
        }

        private void OnEnableDuplicateCheckChanged(object sender, EventArgs e)
        {
            if (checkBoxEnableDuplicateCheck.Checked)
            {
                checkBoxAllowDuplicates.Checked = false;
                _appController.MessageService.ShowInfo("已启用重复检查：将检测并阻止重复数据", this);
            }
        }

        private void OnAllowDuplicatesChanged(object sender, EventArgs e)
        {
            if (checkBoxAllowDuplicates.Checked)
            {
                checkBoxEnableDuplicateCheck.Checked = false;
                _appController.MessageService.ShowInfo("已允许重复数据：重复数据将正常通过", this);
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateServerConfigurationsFromUI()
        {
            if (_serverConfigs?.Length >= 2)
            {
                // 更新服务器1配置
                var server1 = _serverConfigs[0];
                if (System.Net.IPAddress.TryParse(comboBoxServer1IP?.Text, out var ip1))
                {
                    server1.IPAddress = ip1;
                    server1.IsEnabled = !string.IsNullOrEmpty(textBoxServer1Port?.Text);
                }
                if (int.TryParse(textBoxServer1Port?.Text, out var port1))
                {
                    server1.Port = port1;
                }

                // 更新服务器2配置
                var server2 = _serverConfigs[1];
                if (System.Net.IPAddress.TryParse(comboBoxServer2IP?.Text, out var ip2))
                {
                    server2.IPAddress = ip2;
                    server2.IsEnabled = !string.IsNullOrEmpty(textBoxServer2Port?.Text);
                }
                if (int.TryParse(textBoxServer2Port?.Text, out var port2))
                {
                    server2.Port = port2;
                }

                _appController.UpdateServerConfiguration(_serverConfigs);
            }
        }

        private void UpdateFilterConfigurationFromUI()
        {
            if (_filterConfig != null)
            {
                _filterConfig.EnableDuplicateCheck = checkBoxEnableDuplicateCheck?.Checked ?? false;
                _filterConfig.AllowDuplicates = checkBoxAllowDuplicates?.Checked ?? true;

                if (int.TryParse(comboBoxDuplicateInterval?.Text, out var interval))
                {
                    _filterConfig.DuplicateCheckInterval = interval;
                }

                _filterConfig.LabelPrefix = textBoxLabelPrefix?.Text ?? "";
                _filterConfig.CellPrefix = textBoxCellPrefix?.Text ?? "";
                _filterConfig.PCMPrefix = textBoxPCMPrefix?.Text ?? "";
            }
        }

        private void UpdateUIForRunningState(bool isRunning)
        {
            if (buttonStart != null)
                buttonStart.Enabled = !isRunning;
            if (buttonStop != null)
                buttonStop.Enabled = isRunning;

            if (comboBoxServer1IP != null)
                comboBoxServer1IP.Enabled = !isRunning;
            if (textBoxServer1Port != null)
                textBoxServer1Port.Enabled = !isRunning;
            if (comboBoxServer2IP != null)
                comboBoxServer2IP.Enabled = !isRunning;
            if (textBoxServer2Port != null)
                textBoxServer2Port.Enabled = !isRunning;



            // 控制持久通知
            if (_notifications != null)
            {
                if (isRunning)
                {
                    // TCP正在运行，停止持久警告
                    _notifications.StopPersistentTcpWarning();
                }
                else
                {
                    // TCP未运行，开始持久警告
                    _notifications.StartPersistentTcpWarning();
                }
            }
        }


        #endregion

        private void groupBoxDataFilter_Enter(object sender, EventArgs e)
        {

        }
    }
}
