<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{29DD2B32-8A08-4AB9-8431-988471E6C8A4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Socket通讯</RootNamespace>
    <AssemblyName>Socket通讯</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <LangVersion>8.0</LangVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>myicon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <Win32Resource>
    </Win32Resource>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AntdUI, Version=2.0.11.0, Culture=neutral, PublicKeyToken=0e24e5118ea3d6af, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\AntdUI\AntdUI.dll</HintPath>
    </Reference>
    <Reference Include="IrisSkin4, Version=2006.3.22.45, Culture=neutral, PublicKeyToken=127be25a6db25e07, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\IrisSkin4.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="6.0.0" />
  </ItemGroup>
  <!-- 配置DLL文件输出到libs文件夹 -->
  <Target Name="CopyDllsToLibsFolder" AfterTargets="Build">
    <ItemGroup>
      <DllFiles Include="$(OutputPath)*.dll" Exclude="$(OutputPath)$(AssemblyName).dll" />
    </ItemGroup>
    <MakeDir Directories="$(OutputPath)libs" />
    <Copy SourceFiles="@(DllFiles)" DestinationFolder="$(OutputPath)libs" />
    <Delete Files="@(DllFiles)" />
  </Target>
  <!-- 配置运行时程序集探测路径 -->
  <Target Name="CreateAppConfig" AfterTargets="CopyDllsToLibsFolder">
    <PropertyGroup>
      <AppConfigContent>
&lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;configuration&gt;
  &lt;runtime&gt;
    &lt;assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1"&gt;
      &lt;probing privatePath="libs" /&gt;
    &lt;/assemblyBinding&gt;
  &lt;/runtime&gt;
&lt;/configuration&gt;
      </AppConfigContent>
    </PropertyGroup>
    <WriteLinesToFile File="$(OutputPath)$(AssemblyName).exe.config" Lines="$(AppConfigContent)" Overwrite="true" />
  </Target>
  <ItemGroup>
    <!-- Core Layer -->
    <Compile Include="Core\ApplicationController.cs" />
    <Compile Include="Core\Interfaces\ITcpServerService.cs" />
    <Compile Include="Core\Interfaces\IDataProcessingService.cs" />
    <Compile Include="Core\Interfaces\IKeyboardService.cs" />
    <Compile Include="Core\Interfaces\ISystemService.cs" />
    <Compile Include="Core\Interfaces\IMessageService.cs" />
    <Compile Include="Core\Services\TcpServerService.cs" />
    <Compile Include="Core\Services\DataProcessingService.cs" />
    <Compile Include="Core\Services\KeyboardService.cs" />
    <Compile Include="Core\Services\SystemService.cs" />
    <Compile Include="Core\Services\AntdMessageService.cs" />
    <Compile Include="Core\Models\ServerConfiguration.cs" />
    <Compile Include="Core\Models\DataRecord.cs" />
    <Compile Include="Core\Models\AppConfiguration.cs" />
    <!-- Infrastructure Layer -->
    <Compile Include="Infrastructure\Logging\ILogger.cs" />
    <Compile Include="Infrastructure\Logging\FileLogger.cs" />
    <Compile Include="Infrastructure\Configuration\IConfigurationManager.cs" />
    <Compile Include="Infrastructure\Configuration\ConfigurationManager.cs" />
    <!-- Common Layer -->
    <Compile Include="Common\Constants.cs" />
    <Compile Include="Common\Exceptions\TcpServerException.cs" />
    <!-- Presentation Layer -->
    <Compile Include="Presentation\Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Presentation\Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Presentation\Helpers\TrayNotificationManager.cs" />
    <!-- Application Entry Point -->
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Presentation\Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <!-- App.config 将自动生成，不需要手动包含 -->
  <ItemGroup>
    <Content Include="myicon.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>