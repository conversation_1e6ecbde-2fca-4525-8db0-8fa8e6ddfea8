namespace TCP通讯.Presentation.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }

                // 清理自定义资源
                notifyIcon?.Dispose();
                contextMenuStrip?.Dispose();
                if (_systemService != null)
                {
                    _systemService.SystemEvent -= OnSystemEvent;
                }
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxServers = new System.Windows.Forms.GroupBox();
            this.labelServer1IP = new System.Windows.Forms.Label();
            this.comboBoxServer1IP = new AntdUI.Select();
            this.labelServer1Port = new System.Windows.Forms.Label();
            this.textBoxServer1Port = new AntdUI.Input();
            this.labelServer1Status = new System.Windows.Forms.Label();
            this.labelServer2IP = new System.Windows.Forms.Label();
            this.comboBoxServer2IP = new AntdUI.Select();
            this.labelServer2Port = new System.Windows.Forms.Label();
            this.textBoxServer2Port = new AntdUI.Input();
            this.labelServer2Status = new System.Windows.Forms.Label();
            this.buttonStart = new AntdUI.Button();
            this.buttonStop = new AntdUI.Button();
            this.groupBoxDataDisplay = new System.Windows.Forms.GroupBox();
            this.textBoxDataDisplay = new AntdUI.Input();
            this.buttonClear = new AntdUI.Button();
            this.labelFilterStatus = new System.Windows.Forms.Label();
            this.groupBoxDataFilter = new System.Windows.Forms.GroupBox();
            this.checkBoxEnableDuplicateCheck = new System.Windows.Forms.CheckBox();
            this.checkBoxAllowDuplicates = new System.Windows.Forms.CheckBox();
            this.labelDuplicateInterval = new System.Windows.Forms.Label();
            this.comboBoxDuplicateInterval = new AntdUI.Select();
            this.labelLabelPrefix = new System.Windows.Forms.Label();
            this.textBoxLabelPrefix = new AntdUI.Input();
            this.labelCellPrefix = new System.Windows.Forms.Label();
            this.textBoxCellPrefix = new AntdUI.Input();
            this.labelPCMPrefix = new System.Windows.Forms.Label();
            this.textBoxPCMPrefix = new AntdUI.Input();
            this.buttonSaveFilter = new AntdUI.Button();
            this.buttonUnlockFilter = new AntdUI.Button();
            this.groupBoxSettings = new System.Windows.Forms.GroupBox();
            this.buttonAutoStart = new AntdUI.Button();
            this.buttonCancelAutoStart = new AntdUI.Button();
            this.buttonCreateShortcut = new AntdUI.Button();
            this.buttonAbout = new AntdUI.Button();
            this.groupBoxServers.SuspendLayout();
            this.groupBoxDataDisplay.SuspendLayout();
            this.groupBoxDataFilter.SuspendLayout();
            this.groupBoxSettings.SuspendLayout();
            this.SuspendLayout();
            //
            // groupBoxServers
            //
            this.groupBoxServers.Controls.Add(this.labelServer1Status);
            this.groupBoxServers.Controls.Add(this.labelServer1IP);
            this.groupBoxServers.Controls.Add(this.comboBoxServer1IP);
            this.groupBoxServers.Controls.Add(this.labelServer1Port);
            this.groupBoxServers.Controls.Add(this.textBoxServer1Port);
            this.groupBoxServers.Controls.Add(this.labelServer2IP);
            this.groupBoxServers.Controls.Add(this.comboBoxServer2IP);
            this.groupBoxServers.Controls.Add(this.labelServer2Port);
            this.groupBoxServers.Controls.Add(this.textBoxServer2Port);
            this.groupBoxServers.Controls.Add(this.labelServer2Status);
            this.groupBoxServers.Controls.Add(this.buttonStart);
            this.groupBoxServers.Controls.Add(this.buttonStop);
            this.groupBoxServers.Location = new System.Drawing.Point(12, 12);
            this.groupBoxServers.Name = "groupBoxServers";
            this.groupBoxServers.Size = new System.Drawing.Size(760, 120);
            this.groupBoxServers.TabIndex = 0;
            this.groupBoxServers.TabStop = false;
            this.groupBoxServers.Text = "扫码枪设置";
            //
            // labelServer1IP
            //
            this.labelServer1IP.AutoSize = true;
            this.labelServer1IP.Location = new System.Drawing.Point(10, 25);
            this.labelServer1IP.Name = "labelServer1IP";
            this.labelServer1IP.Size = new System.Drawing.Size(71, 12);
            this.labelServer1IP.TabIndex = 0;
            this.labelServer1IP.Text = "扫码枪1 IP:";
            //
            // comboBoxServer1IP
            //
            this.comboBoxServer1IP.Location = new System.Drawing.Point(85, 22);
            this.comboBoxServer1IP.Name = "comboBoxServer1IP";
            this.comboBoxServer1IP.Size = new System.Drawing.Size(120, 32);
            this.comboBoxServer1IP.TabIndex = 1;
            this.comboBoxServer1IP.PlaceholderText = "选择IP地址";
            //
            // labelServer1Port
            //
            this.labelServer1Port.AutoSize = true;
            this.labelServer1Port.Location = new System.Drawing.Point(220, 25);
            this.labelServer1Port.Name = "labelServer1Port";
            this.labelServer1Port.Size = new System.Drawing.Size(35, 12);
            this.labelServer1Port.TabIndex = 2;
            this.labelServer1Port.Text = "端口:";
            //
            // textBoxServer1Port
            //
            this.textBoxServer1Port.Location = new System.Drawing.Point(265, 22);
            this.textBoxServer1Port.Name = "textBoxServer1Port";
            this.textBoxServer1Port.Size = new System.Drawing.Size(60, 32);
            this.textBoxServer1Port.TabIndex = 3;
            this.textBoxServer1Port.PlaceholderText = "端口号";
            //
            // labelServer1Status
            //
            this.labelServer1Status.AutoSize = true;
            this.labelServer1Status.BackColor = System.Drawing.Color.Yellow;
            this.labelServer1Status.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.labelServer1Status.Location = new System.Drawing.Point(340, 23);
            this.labelServer1Status.Name = "labelServer1Status";
            this.labelServer1Status.Size = new System.Drawing.Size(55, 14);
            this.labelServer1Status.TabIndex = 4;
            this.labelServer1Status.Text = "等待连接";
            this.labelServer1Status.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.labelServer1Status.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // labelServer2IP
            // 
            this.labelServer2IP.AutoSize = true;
            this.labelServer2IP.Location = new System.Drawing.Point(10, 55);
            this.labelServer2IP.Name = "labelServer2IP";
            this.labelServer2IP.Size = new System.Drawing.Size(71, 12);
            this.labelServer2IP.TabIndex = 5;
            this.labelServer2IP.Text = "扫码枪2 IP:";
            //
            // comboBoxServer2IP
            //
            this.comboBoxServer2IP.Location = new System.Drawing.Point(85, 52);
            this.comboBoxServer2IP.Name = "comboBoxServer2IP";
            this.comboBoxServer2IP.Size = new System.Drawing.Size(120, 32);
            this.comboBoxServer2IP.TabIndex = 6;
            this.comboBoxServer2IP.PlaceholderText = "选择IP地址";
            // 
            // labelServer2Port
            // 
            this.labelServer2Port.AutoSize = true;
            this.labelServer2Port.Location = new System.Drawing.Point(220, 55);
            this.labelServer2Port.Name = "labelServer2Port";
            this.labelServer2Port.Size = new System.Drawing.Size(35, 12);
            this.labelServer2Port.TabIndex = 7;
            this.labelServer2Port.Text = "端口:";
            // 
            // textBoxServer2Port
            // 
            this.textBoxServer2Port.Location = new System.Drawing.Point(265, 52);
            this.textBoxServer2Port.Name = "textBoxServer2Port";
            this.textBoxServer2Port.Size = new System.Drawing.Size(60, 21);
            this.textBoxServer2Port.TabIndex = 8;
            // 
            // labelServer2Status
            // 
            this.labelServer2Status.AutoSize = true;
            this.labelServer2Status.BackColor = System.Drawing.Color.Yellow;
            this.labelServer2Status.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.labelServer2Status.Location = new System.Drawing.Point(340, 55);
            this.labelServer2Status.Name = "labelServer2Status";
            this.labelServer2Status.Size = new System.Drawing.Size(55, 14);
            this.labelServer2Status.TabIndex = 9;
            this.labelServer2Status.Text = "等待连接";
            this.labelServer2Status.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            //
            // buttonStart
            //
            this.buttonStart.Location = new System.Drawing.Point(500, 20);
            this.buttonStart.Name = "buttonStart";
            this.buttonStart.Size = new System.Drawing.Size(75, 32);
            this.buttonStart.TabIndex = 9;
            this.buttonStart.Text = "开始监听";
            this.buttonStart.Type = AntdUI.TTypeMini.Primary;
            this.buttonStart.Click += new System.EventHandler(this.OnStartButtonClick);
            //
            // buttonStop
            //
            this.buttonStop.Enabled = false;
            this.buttonStop.Location = new System.Drawing.Point(590, 20);
            this.buttonStop.Name = "buttonStop";
            this.buttonStop.Size = new System.Drawing.Size(75, 32);
            this.buttonStop.TabIndex = 10;
            this.buttonStop.Text = "停止监听";
            this.buttonStop.Type = AntdUI.TTypeMini.Default;
            this.buttonStop.Click += new System.EventHandler(this.OnStopButtonClick);
            // 
            // groupBoxDataDisplay
            // 
            this.groupBoxDataDisplay.Controls.Add(this.textBoxDataDisplay);
            this.groupBoxDataDisplay.Controls.Add(this.buttonClear);
            this.groupBoxDataDisplay.Controls.Add(this.labelFilterStatus);
            this.groupBoxDataDisplay.Location = new System.Drawing.Point(12, 140);
            this.groupBoxDataDisplay.Name = "groupBoxDataDisplay";
            this.groupBoxDataDisplay.Size = new System.Drawing.Size(760, 200);
            this.groupBoxDataDisplay.TabIndex = 1;
            this.groupBoxDataDisplay.TabStop = false;
            this.groupBoxDataDisplay.Text = "数据显示";
            //
            // textBoxDataDisplay
            //
            this.textBoxDataDisplay.Location = new System.Drawing.Point(15, 20);
            this.textBoxDataDisplay.Multiline = true;
            this.textBoxDataDisplay.Name = "textBoxDataDisplay";
            this.textBoxDataDisplay.ReadOnly = true;
            this.textBoxDataDisplay.Size = new System.Drawing.Size(650, 140);
            this.textBoxDataDisplay.TabIndex = 0;
            //
            // buttonClear
            //
            this.buttonClear.Location = new System.Drawing.Point(680, 20);
            this.buttonClear.Name = "buttonClear";
            this.buttonClear.Size = new System.Drawing.Size(74, 32);
            this.buttonClear.TabIndex = 1;
            this.buttonClear.Text = "清空数据";
            this.buttonClear.Type = AntdUI.TTypeMini.Default;
            this.buttonClear.Click += new System.EventHandler(this.OnClearButtonClick);
            // 
            // labelFilterStatus
            // 
            this.labelFilterStatus.AutoSize = true;
            this.labelFilterStatus.Location = new System.Drawing.Point(15, 170);
            this.labelFilterStatus.Name = "labelFilterStatus";
            this.labelFilterStatus.Size = new System.Drawing.Size(0, 12);
            this.labelFilterStatus.TabIndex = 2;
            // 
            // groupBoxDataFilter
            // 
            this.groupBoxDataFilter.Controls.Add(this.checkBoxEnableDuplicateCheck);
            this.groupBoxDataFilter.Controls.Add(this.checkBoxAllowDuplicates);
            this.groupBoxDataFilter.Controls.Add(this.labelDuplicateInterval);
            this.groupBoxDataFilter.Controls.Add(this.comboBoxDuplicateInterval);
            this.groupBoxDataFilter.Controls.Add(this.labelLabelPrefix);
            this.groupBoxDataFilter.Controls.Add(this.textBoxLabelPrefix);
            this.groupBoxDataFilter.Controls.Add(this.labelCellPrefix);
            this.groupBoxDataFilter.Controls.Add(this.textBoxCellPrefix);
            this.groupBoxDataFilter.Controls.Add(this.labelPCMPrefix);
            this.groupBoxDataFilter.Controls.Add(this.textBoxPCMPrefix);
            this.groupBoxDataFilter.Controls.Add(this.buttonSaveFilter);
            this.groupBoxDataFilter.Controls.Add(this.buttonUnlockFilter);
            this.groupBoxDataFilter.Location = new System.Drawing.Point(12, 350);
            this.groupBoxDataFilter.Name = "groupBoxDataFilter";
            this.groupBoxDataFilter.Size = new System.Drawing.Size(760, 120);
            this.groupBoxDataFilter.TabIndex = 2;
            this.groupBoxDataFilter.TabStop = false;
            this.groupBoxDataFilter.Text = "信息配置";
            this.groupBoxDataFilter.Enter += new System.EventHandler(this.groupBoxDataFilter_Enter);
            // 
            // checkBoxEnableDuplicateCheck
            // 
            this.checkBoxEnableDuplicateCheck.AutoSize = true;
            this.checkBoxEnableDuplicateCheck.Location = new System.Drawing.Point(15, 25);
            this.checkBoxEnableDuplicateCheck.Name = "checkBoxEnableDuplicateCheck";
            this.checkBoxEnableDuplicateCheck.Size = new System.Drawing.Size(96, 16);
            this.checkBoxEnableDuplicateCheck.TabIndex = 0;
            this.checkBoxEnableDuplicateCheck.Text = "启用重复检查";
            this.checkBoxEnableDuplicateCheck.UseVisualStyleBackColor = true;
            this.checkBoxEnableDuplicateCheck.CheckedChanged += new System.EventHandler(this.OnEnableDuplicateCheckChanged);
            // 
            // checkBoxAllowDuplicates
            // 
            this.checkBoxAllowDuplicates.AutoSize = true;
            this.checkBoxAllowDuplicates.Location = new System.Drawing.Point(130, 25);
            this.checkBoxAllowDuplicates.Name = "checkBoxAllowDuplicates";
            this.checkBoxAllowDuplicates.Size = new System.Drawing.Size(96, 16);
            this.checkBoxAllowDuplicates.TabIndex = 1;
            this.checkBoxAllowDuplicates.Text = "允许重复数据";
            this.checkBoxAllowDuplicates.UseVisualStyleBackColor = true;
            this.checkBoxAllowDuplicates.CheckedChanged += new System.EventHandler(this.OnAllowDuplicatesChanged);
            // 
            // labelDuplicateInterval
            // 
            this.labelDuplicateInterval.AutoSize = true;
            this.labelDuplicateInterval.Location = new System.Drawing.Point(230, 27);
            this.labelDuplicateInterval.Name = "labelDuplicateInterval";
            this.labelDuplicateInterval.Size = new System.Drawing.Size(83, 12);
            this.labelDuplicateInterval.TabIndex = 2;
            this.labelDuplicateInterval.Text = "时间间隔(分):";
            //
            // comboBoxDuplicateInterval
            //
            this.comboBoxDuplicateInterval.Location = new System.Drawing.Point(315, 24);
            this.comboBoxDuplicateInterval.Name = "comboBoxDuplicateInterval";
            this.comboBoxDuplicateInterval.Size = new System.Drawing.Size(60, 32);
            this.comboBoxDuplicateInterval.TabIndex = 3;
            this.comboBoxDuplicateInterval.PlaceholderText = "分钟";
            // 
            // labelLabelPrefix
            // 
            this.labelLabelPrefix.AutoSize = true;
            this.labelLabelPrefix.Location = new System.Drawing.Point(26, 70);
            this.labelLabelPrefix.Name = "labelLabelPrefix";
            this.labelLabelPrefix.Size = new System.Drawing.Size(53, 12);
            this.labelLabelPrefix.TabIndex = 4;
            this.labelLabelPrefix.Text = "Label码:";
            //
            // textBoxLabelPrefix
            //
            this.textBoxLabelPrefix.Location = new System.Drawing.Point(85, 67);
            this.textBoxLabelPrefix.Name = "textBoxLabelPrefix";
            this.textBoxLabelPrefix.Size = new System.Drawing.Size(80, 32);
            this.textBoxLabelPrefix.TabIndex = 5;
            this.textBoxLabelPrefix.PlaceholderText = "Label前缀";
            // 
            // labelCellPrefix
            // 
            this.labelCellPrefix.AutoSize = true;
            this.labelCellPrefix.Location = new System.Drawing.Point(197, 71);
            this.labelCellPrefix.Name = "labelCellPrefix";
            this.labelCellPrefix.Size = new System.Drawing.Size(47, 12);
            this.labelCellPrefix.TabIndex = 6;
            this.labelCellPrefix.Text = "CELL码:";
            //
            // textBoxCellPrefix
            //
            this.textBoxCellPrefix.Location = new System.Drawing.Point(250, 67);
            this.textBoxCellPrefix.Name = "textBoxCellPrefix";
            this.textBoxCellPrefix.Size = new System.Drawing.Size(80, 32);
            this.textBoxCellPrefix.TabIndex = 7;
            this.textBoxCellPrefix.PlaceholderText = "CELL前缀";
            // 
            // labelPCMPrefix
            // 
            this.labelPCMPrefix.AutoSize = true;
            this.labelPCMPrefix.Location = new System.Drawing.Point(338, 71);
            this.labelPCMPrefix.Name = "labelPCMPrefix";
            this.labelPCMPrefix.Size = new System.Drawing.Size(41, 12);
            this.labelPCMPrefix.TabIndex = 8;
            this.labelPCMPrefix.Text = "PCM码:";
            //
            // textBoxPCMPrefix
            //
            this.textBoxPCMPrefix.Location = new System.Drawing.Point(385, 68);
            this.textBoxPCMPrefix.Name = "textBoxPCMPrefix";
            this.textBoxPCMPrefix.Size = new System.Drawing.Size(80, 32);
            this.textBoxPCMPrefix.TabIndex = 9;
            this.textBoxPCMPrefix.PlaceholderText = "PCM前缀";
            //
            // buttonSaveFilter
            //
            this.buttonSaveFilter.Location = new System.Drawing.Point(580, 50);
            this.buttonSaveFilter.Name = "buttonSaveFilter";
            this.buttonSaveFilter.Size = new System.Drawing.Size(75, 32);
            this.buttonSaveFilter.TabIndex = 10;
            this.buttonSaveFilter.Text = "保存配置";
            this.buttonSaveFilter.Type = AntdUI.TTypeMini.Primary;
            this.buttonSaveFilter.Click += new System.EventHandler(this.OnSaveFilterButtonClick);
            //
            // buttonUnlockFilter
            //
            this.buttonUnlockFilter.Enabled = false;
            this.buttonUnlockFilter.Location = new System.Drawing.Point(665, 50);
            this.buttonUnlockFilter.Name = "buttonUnlockFilter";
            this.buttonUnlockFilter.Size = new System.Drawing.Size(75, 32);
            this.buttonUnlockFilter.TabIndex = 11;
            this.buttonUnlockFilter.Text = "解锁配置";
            this.buttonUnlockFilter.Type = AntdUI.TTypeMini.Default;
            this.buttonUnlockFilter.Click += new System.EventHandler(this.OnUnlockFilterButtonClick);
            // 
            // groupBoxSettings
            // 
            this.groupBoxSettings.Controls.Add(this.buttonAutoStart);
            this.groupBoxSettings.Controls.Add(this.buttonCancelAutoStart);
            this.groupBoxSettings.Controls.Add(this.buttonCreateShortcut);
            this.groupBoxSettings.Controls.Add(this.buttonAbout);
            this.groupBoxSettings.Location = new System.Drawing.Point(12, 480);
            this.groupBoxSettings.Name = "groupBoxSettings";
            this.groupBoxSettings.Size = new System.Drawing.Size(760, 60);
            this.groupBoxSettings.TabIndex = 3;
            this.groupBoxSettings.TabStop = false;
            this.groupBoxSettings.Text = "系统设置";
            //
            // buttonAutoStart
            //
            this.buttonAutoStart.Location = new System.Drawing.Point(15, 25);
            this.buttonAutoStart.Name = "buttonAutoStart";
            this.buttonAutoStart.Size = new System.Drawing.Size(100, 32);
            this.buttonAutoStart.TabIndex = 0;
            this.buttonAutoStart.Text = "设置开机自启";
            this.buttonAutoStart.Type = AntdUI.TTypeMini.Default;
            this.buttonAutoStart.Click += new System.EventHandler(this.OnAutoStartButtonClick);
            //
            // buttonCancelAutoStart
            //
            this.buttonCancelAutoStart.Location = new System.Drawing.Point(125, 25);
            this.buttonCancelAutoStart.Name = "buttonCancelAutoStart";
            this.buttonCancelAutoStart.Size = new System.Drawing.Size(100, 32);
            this.buttonCancelAutoStart.TabIndex = 1;
            this.buttonCancelAutoStart.Text = "取消开机自启";
            this.buttonCancelAutoStart.Type = AntdUI.TTypeMini.Default;
            this.buttonCancelAutoStart.Click += new System.EventHandler(this.OnCancelAutoStartButtonClick);
            //
            // buttonCreateShortcut
            //
            this.buttonCreateShortcut.Location = new System.Drawing.Point(235, 25);
            this.buttonCreateShortcut.Name = "buttonCreateShortcut";
            this.buttonCreateShortcut.Size = new System.Drawing.Size(100, 32);
            this.buttonCreateShortcut.TabIndex = 2;
            this.buttonCreateShortcut.Text = "创建桌面快捷方式";
            this.buttonCreateShortcut.Type = AntdUI.TTypeMini.Default;
            this.buttonCreateShortcut.Click += new System.EventHandler(this.OnCreateShortcutButtonClick);
            //
            // buttonAbout
            //
            this.buttonAbout.Location = new System.Drawing.Point(665, 25);
            this.buttonAbout.Name = "buttonAbout";
            this.buttonAbout.Size = new System.Drawing.Size(75, 32);
            this.buttonAbout.TabIndex = 3;
            this.buttonAbout.Text = "关于";
            this.buttonAbout.Type = AntdUI.TTypeMini.Default;
            this.buttonAbout.Click += new System.EventHandler(this.OnAboutButtonClick);
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 550);
            this.Controls.Add(this.groupBoxServers);
            this.Controls.Add(this.groupBoxDataDisplay);
            this.Controls.Add(this.groupBoxDataFilter);
            this.Controls.Add(this.groupBoxSettings);
            this.Name = "MainForm";
            this.Text = "TCP通讯";
            this.groupBoxServers.ResumeLayout(false);
            this.groupBoxServers.PerformLayout();
            this.groupBoxDataDisplay.ResumeLayout(false);
            this.groupBoxDataDisplay.PerformLayout();
            this.groupBoxDataFilter.ResumeLayout(false);
            this.groupBoxDataFilter.PerformLayout();
            this.groupBoxSettings.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label labelServer1IP;
        private System.Windows.Forms.Label labelServer1Port;
        private System.Windows.Forms.Label labelServer2IP;
        private System.Windows.Forms.Label labelServer2Port;
        private System.Windows.Forms.Label labelDuplicateInterval;
        private System.Windows.Forms.Label labelLabelPrefix;
        private System.Windows.Forms.Label labelCellPrefix;
        private System.Windows.Forms.Label labelPCMPrefix;
    }
}
