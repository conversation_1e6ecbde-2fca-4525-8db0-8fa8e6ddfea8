using System;
using System.Windows.Forms;

namespace TCP通讯.Presentation.Helpers
{
    /// <summary>
    /// 系统托盘通知管理器
    /// 提供类似React Ant Design的简洁API来显示各种类型的通知
    /// </summary>
    public class TrayNotificationManager
    {
        private readonly Form _parentForm;
        private readonly string _defaultTitle;
        private System.Windows.Forms.Timer _persistentNotificationTimer;
        private bool _showPersistentWarning = false;

        /// <summary>
        /// 初始化通知管理器
        /// </summary>
        /// <param name="parentForm">所属窗体</param>
        /// <param name="defaultTitle">默认通知标题</param>
        public TrayNotificationManager(Form parentForm, string defaultTitle = "TCP通讯")
        {
            _parentForm = parentForm ?? throw new ArgumentNullException(nameof(parentForm));
            _defaultTitle = defaultTitle;

            // 初始化持久通知定时器
            _persistentNotificationTimer = new System.Windows.Forms.Timer();
            _persistentNotificationTimer.Interval = 30000; // 30秒间隔
            _persistentNotificationTimer.Tick += OnPersistentNotificationTimer;
        }

        /// <summary>
        /// 显示成功通知（绿色图标）
        /// </summary>
        /// <param name="message">通知内容</param>
        /// <param name="title">通知标题，为空时使用默认标题</param>
        /// <param name="autoClose">自动关闭时间（秒），0表示不自动关闭</param>
        public void Success(string message, string title = null, int autoClose = 3)
        {
            ShowNotification(message, title ?? _defaultTitle, AntdUI.TType.Success, autoClose);
        }

        /// <summary>
        /// 显示信息通知（蓝色图标）
        /// </summary>
        /// <param name="message">通知内容</param>
        /// <param name="title">通知标题，为空时使用默认标题</param>
        /// <param name="autoClose">自动关闭时间（秒），0表示不自动关闭</param>
        public void Info(string message, string title = null, int autoClose = 3)
        {
            ShowNotification(message, title ?? _defaultTitle, AntdUI.TType.Info, autoClose);
        }

        /// <summary>
        /// 显示警告通知（黄色图标）
        /// </summary>
        /// <param name="message">通知内容</param>
        /// <param name="title">通知标题，为空时使用默认标题</param>
        /// <param name="autoClose">自动关闭时间（秒），0表示不自动关闭</param>
        public void Warning(string message, string title = null, int autoClose = 3)
        {
            ShowNotification(message, title ?? _defaultTitle, AntdUI.TType.Warn, autoClose);
        }

        /// <summary>
        /// 显示错误通知（红色图标）
        /// </summary>
        /// <param name="message">通知内容</param>
        /// <param name="title">通知标题，为空时使用默认标题</param>
        /// <param name="autoClose">自动关闭时间（秒），0表示不自动关闭</param>
        public void Error(string message, string title = null, int autoClose = 5)
        {
            ShowNotification(message, title ?? _defaultTitle, AntdUI.TType.Error, autoClose);
        }

        /// <summary>
        /// 显示自定义通知
        /// </summary>
        /// <param name="message">通知内容</param>
        /// <param name="title">通知标题</param>
        /// <param name="iconType">图标类型</param>
        /// <param name="autoClose">自动关闭时间（秒），0表示不自动关闭</param>
        /// <param name="showInWindow">是否在窗体内显示</param>
        public void Custom(string message, string title, AntdUI.TType iconType, int autoClose = 3, bool showInWindow = false)
        {
            ShowNotification(message, title, iconType, autoClose, showInWindow);
        }

        /// <summary>
        /// 核心通知显示方法
        /// </summary>
        private void ShowNotification(string message, string title, AntdUI.TType iconType, int autoClose = 3, bool showInWindow = false)
        {
            if (string.IsNullOrWhiteSpace(message))
                throw new ArgumentException("通知内容不能为空", nameof(message));

            // 确保在主线程中显示通知
            if (_parentForm.InvokeRequired)
            {
                _parentForm.Invoke(new Action(() =>
                {
                    // 使用简化的通知方法
                    switch (iconType)
                    {
                        case AntdUI.TType.Success:
                            AntdUI.Notification.success(_parentForm, title, message, autoClose: autoClose);
                            break;
                        case AntdUI.TType.Info:
                            AntdUI.Notification.info(_parentForm, title, message, autoClose: autoClose);
                            break;
                        case AntdUI.TType.Warn:
                            AntdUI.Notification.warn(_parentForm, title, message, autoClose: autoClose);
                            break;
                        case AntdUI.TType.Error:
                            AntdUI.Notification.error(_parentForm, title, message, autoClose: autoClose);
                            break;
                        default:
                            AntdUI.Notification.open(_parentForm, title, message, autoClose: autoClose);
                            break;
                    }
                }));
            }
            else
            {
                // 使用简化的通知方法
                switch (iconType)
                {
                    case AntdUI.TType.Success:
                        AntdUI.Notification.success(_parentForm, title, message, autoClose: autoClose);
                        break;
                    case AntdUI.TType.Info:
                        AntdUI.Notification.info(_parentForm, title, message, autoClose: autoClose);
                        break;
                    case AntdUI.TType.Warn:
                        AntdUI.Notification.warn(_parentForm, title, message, autoClose: autoClose);
                        break;
                    case AntdUI.TType.Error:
                        AntdUI.Notification.error(_parentForm, title, message, autoClose: autoClose);
                        break;
                    default:
                        AntdUI.Notification.open(_parentForm, title, message, autoClose: autoClose);
                        break;
                }
            }
        }

        /// <summary>
        /// 开始显示TCP未运行的持久警告通知
        /// </summary>
        public void StartPersistentTcpWarning()
        {
            _showPersistentWarning = true;
            _persistentNotificationTimer.Start();

            // 立即显示第一次通知
            ShowTcpNotRunningWarning();
        }

        /// <summary>
        /// 停止显示TCP未运行的持久警告通知
        /// </summary>
        public void StopPersistentTcpWarning()
        {
            _showPersistentWarning = false;
            _persistentNotificationTimer.Stop();
        }

        /// <summary>
        /// 设置持久通知的间隔时间
        /// </summary>
        /// <param name="intervalSeconds">间隔秒数</param>
        public void SetPersistentNotificationInterval(int intervalSeconds)
        {
            if (intervalSeconds < 5) intervalSeconds = 5; // 最小5秒
            _persistentNotificationTimer.Interval = intervalSeconds * 1000;
        }

        /// <summary>
        /// 持久通知定时器事件
        /// </summary>
        private void OnPersistentNotificationTimer(object sender, EventArgs e)
        {
            if (_showPersistentWarning)
            {
                ShowTcpNotRunningWarning();
            }
        }

        /// <summary>
        /// 显示TCP未运行警告
        /// </summary>
        private void ShowTcpNotRunningWarning()
        {
            Warning("TCP端口未运行，请及时运行", "系统提醒", autoClose: 8);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _persistentNotificationTimer?.Stop();
            _persistentNotificationTimer?.Dispose();
        }

        // 注意：AntdUI.Notification 可能不支持程序化关闭通知的功能
        // 如果需要这些功能，可以在未来的版本中添加
    }
}
